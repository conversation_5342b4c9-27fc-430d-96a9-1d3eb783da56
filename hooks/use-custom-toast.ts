import { useCallback } from 'react'

export interface CustomToastOptions {
  title: string
  description?: string
  type?: 'success' | 'error' | 'warning' | 'info'
  duration?: number
}

export function useCustomToast() {
  const showToast = useCallback((options: CustomToastOptions) => {
    // 使用全局方法显示toast
    if (typeof window !== 'undefined' && (window as any).showCustomToast) {
      (window as any).showCustomToast(options)
    } else {
      // 如果全局方法不可用，fallback到console
      console.log('Toast:', options.title, options.description)
    }
  }, [])

  const success = useCallback((title: string, description?: string) => {
    showToast({ title, description, type: 'success' })
  }, [showToast])

  const error = useCallback((title: string, description?: string) => {
    showToast({ title, description, type: 'error' })
  }, [showToast])

  const warning = useCallback((title: string, description?: string) => {
    showToast({ title, description, type: 'warning' })
  }, [showToast])

  const info = useCallback((title: string, description?: string) => {
    showToast({ title, description, type: 'info' })
  }, [showToast])

  return {
    showToast,
    success,
    error,
    warning,
    info
  }
}
