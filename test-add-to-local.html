<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>添加到本站功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <h1>ToolMaster "添加到本站" 功能测试</h1>
    
    <div class="test-section success">
        <h2>✅ 问题已修复</h2>
        <p><strong>主要修复内容：</strong></p>
        <ul>
            <li>修复了 <code>storageManager</code> 未定义的问题，改为使用 <code>hybridStorage</code></li>
            <li>添加了详细的调试日志，便于追踪问题</li>
            <li>增加了500ms延迟等待实时更新生效</li>
            <li>添加成功后自动清除搜索状态并返回主页</li>
        </ul>
    </div>

    <div class="test-section info">
        <h2>🧪 测试步骤</h2>
        <div class="step">
            <strong>步骤 1:</strong> 打开主应用 (http://localhost:3001)
        </div>
        <div class="step">
            <strong>步骤 2:</strong> 在搜索框输入 "macos好用的压缩软件"
        </div>
        <div class="step">
            <strong>步骤 3:</strong> 点击 "全网搜索" 按钮
        </div>
        <div class="step">
            <strong>步骤 4:</strong> 等待搜索结果显示（应该显示 The Unarchiver、Keka 等工具）
        </div>
        <div class="step">
            <strong>步骤 5:</strong> 点击任意工具卡片右上角的 "⋮" 按钮
        </div>
        <div class="step">
            <strong>步骤 6:</strong> 选择 "添加到本站" 选项
        </div>
        <div class="step">
            <strong>步骤 7:</strong> 观察是否显示 "添加成功" 的提示
        </div>
        <div class="step">
            <strong>步骤 8:</strong> 页面应该自动返回主页，新添加的工具应该出现在工具列表中
        </div>
    </div>

    <div class="test-section warning">
        <h2>🔍 调试信息</h2>
        <p>如果仍然有问题，请检查浏览器控制台的调试信息：</p>
        <ul>
            <li><code>开始添加全网搜索工具到本站: [工具名称]</code></li>
            <li><code>准备添加的工具数据: [工具数据对象]</code></li>
            <li><code>调用 hybridStorage.addTool...</code></li>
            <li><code>hybridStorage.addTool 返回结果: [返回结果]</code></li>
            <li><code>工具添加成功: [工具名称] ID: [工具ID]</code></li>
            <li><code>强制刷新工具列表...</code></li>
            <li><code>当前工具数量: [数量]</code></li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🔧 可能的问题和解决方案</h2>
        
        <h3>问题1: 仍然显示 "storageManager is not defined"</h3>
        <p><strong>解决方案:</strong> 确保开发服务器已重启，修改已生效</p>
        
        <h3>问题2: 添加成功但工具没有显示</h3>
        <p><strong>解决方案:</strong> 检查以下几点：</p>
        <ul>
            <li>Supabase 连接是否正常</li>
            <li>实时更新是否工作正常</li>
            <li>工具是否真的添加到了数据库</li>
        </ul>
        
        <h3>问题3: 添加失败</h3>
        <p><strong>解决方案:</strong> 检查控制台错误信息，可能的原因：</p>
        <ul>
            <li>网络连接问题</li>
            <li>Supabase 配置问题</li>
            <li>数据格式问题</li>
        </ul>
    </div>

    <div class="test-section info">
        <h2>📋 预期行为</h2>
        <ol>
            <li>点击 "添加到本站" 后，应该显示绿色的成功提示</li>
            <li>该工具应该从全网搜索结果中消失</li>
            <li>页面应该自动返回主页（清除搜索状态）</li>
            <li>新添加的工具应该出现在主页工具列表的顶部</li>
            <li>刷新页面后，工具仍然存在（说明已保存到数据库）</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>🎯 测试建议</h2>
        <p>建议测试以下场景：</p>
        <ul>
            <li><strong>正常添加:</strong> 添加一个新工具，确认成功</li>
            <li><strong>重复检查:</strong> 尝试添加已存在的工具，应该显示 "工具已存在" 提示</li>
            <li><strong>多个添加:</strong> 连续添加多个不同的工具</li>
            <li><strong>页面刷新:</strong> 添加后刷新页面，确认工具仍然存在</li>
        </ul>
    </div>
</body>
</html>
