<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ToolMaster 命名一致性测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-section.success {
            background: #d4edda;
            border-color: #c3e6cb;
        }
        .test-section.warning {
            background: #fff3cd;
            border-color: #ffeaa7;
        }
        .test-section.error {
            background: #f8d7da;
            border-color: #f5c6cb;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            background: #ffffff;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-pass { background: #28a745; }
        .status-fail { background: #dc3545; }
        .status-pending { background: #ffc107; }
        h1 { color: #2c3e50; }
        h2 { color: #34495e; }
        .code { 
            background: #f1f3f4; 
            padding: 2px 6px; 
            border-radius: 3px; 
            font-family: monospace; 
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <h1>🔧 ToolMaster 命名一致性测试</h1>
    
    <div class="test-section">
        <h2>📋 测试概述</h2>
        <p>本测试验证项目中所有 "toolshift" 到 "toolmaster" 的命名修改是否正确生效，包括：</p>
        <ul>
            <li><strong>组件名称</strong>：React 组件导出名称</li>
            <li><strong>User-Agent</strong>：API 请求中的标识</li>
            <li><strong>导出文件名</strong>：用户下载的文件命名</li>
            <li><strong>导出元数据</strong>：JSON 数据中的标识信息</li>
            <li><strong>日志存储</strong>：localStorage 中的日志键名</li>
            <li><strong>数据迁移</strong>：旧数据的自动迁移功能</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>🧪 自动化测试</h2>
        <button class="test-button" onclick="runAllTests()">运行所有测试</button>
        <button class="test-button" onclick="clearTestResults()">清除结果</button>
        <div id="testResults"></div>
    </div>

    <div class="test-section">
        <h2>📊 测试项目</h2>
        
        <h3>1. 日志数据迁移测试</h3>
        <p>测试旧版本日志数据是否能正确迁移到新的键名</p>
        <button class="test-button" onclick="testLogMigration()">测试日志迁移</button>
        <div id="logMigrationResult" class="test-result" style="display: none;"></div>

        <h3>2. 导出功能测试</h3>
        <p>测试导出文件的命名和元数据是否使用新的项目名称</p>
        <button class="test-button" onclick="testExportNaming()">测试导出命名</button>
        <div id="exportNamingResult" class="test-result" style="display: none;"></div>

        <h3>3. localStorage 键名测试</h3>
        <p>验证所有 localStorage 操作使用正确的键名</p>
        <button class="test-button" onclick="testLocalStorageKeys()">测试存储键名</button>
        <div id="storageKeysResult" class="test-result" style="display: none;"></div>
    </div>

    <div class="test-section warning">
        <h2>⚠️ 手动验证项目</h2>
        <p>以下项目需要手动验证：</p>
        <ol>
            <li><strong>组件名称</strong>：检查浏览器开发者工具中的 React 组件名称是否为 <code class="code">ToolMasterApp</code></li>
            <li><strong>User-Agent</strong>：在网络请求中查看 User-Agent 是否包含 <code class="code">ToolMaster/1.0</code></li>
            <li><strong>导出文件</strong>：执行导出操作，检查下载的文件名是否以 <code class="code">toolmaster-</code> 开头</li>
            <li><strong>导出内容</strong>：打开导出的 JSON 文件，检查 <code class="code">type</code> 和 <code class="code">exportedBy</code> 字段</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>🔍 调试信息</h2>
        <button class="test-button" onclick="showDebugInfo()">显示调试信息</button>
        <div id="debugInfo" class="test-result" style="display: none;"></div>
    </div>

    <script>
        // 测试结果存储
        let testResults = [];

        // 运行所有测试
        function runAllTests() {
            clearTestResults();
            testLogMigration();
            testExportNaming();
            testLocalStorageKeys();
            
            setTimeout(() => {
                displayTestSummary();
            }, 1000);
        }

        // 清除测试结果
        function clearTestResults() {
            testResults = [];
            document.getElementById('testResults').innerHTML = '';
            ['logMigrationResult', 'exportNamingResult', 'storageKeysResult'].forEach(id => {
                const element = document.getElementById(id);
                element.style.display = 'none';
                element.innerHTML = '';
            });
        }

        // 测试日志迁移功能
        function testLogMigration() {
            const resultDiv = document.getElementById('logMigrationResult');
            resultDiv.style.display = 'block';
            
            let result = '🔄 测试日志数据迁移功能...\n\n';
            
            try {
                // 清理现有数据
                localStorage.removeItem('toolmaster-logs');
                localStorage.removeItem('toolshift-logs');
                
                // 模拟旧版本数据
                const oldLogData = JSON.stringify([
                    { action: 'TEST_OLD_LOG', timestamp: new Date().toISOString() }
                ]);
                localStorage.setItem('toolshift-logs', oldLogData);
                
                result += '✅ 步骤1: 创建模拟旧版本日志数据\n';
                result += `   旧数据: ${oldLogData}\n\n`;
                
                // 触发迁移（模拟页面重新加载）
                const oldLogs = localStorage.getItem("toolshift-logs");
                if (oldLogs && !localStorage.getItem("toolmaster-logs")) {
                    localStorage.setItem("toolmaster-logs", oldLogs);
                    localStorage.removeItem("toolshift-logs");
                    result += '✅ 步骤2: 数据迁移成功执行\n';
                } else {
                    result += '❌ 步骤2: 数据迁移条件不满足\n';
                }
                
                // 验证迁移结果
                const newLogs = localStorage.getItem('toolmaster-logs');
                const oldLogsAfter = localStorage.getItem('toolshift-logs');
                
                if (newLogs && !oldLogsAfter) {
                    result += '✅ 步骤3: 迁移验证成功\n';
                    result += `   新数据: ${newLogs}\n`;
                    result += '   旧数据: 已删除\n';
                    testResults.push({ name: '日志迁移', status: 'pass' });
                } else {
                    result += '❌ 步骤3: 迁移验证失败\n';
                    result += `   新数据: ${newLogs || '无'}\n`;
                    result += `   旧数据: ${oldLogsAfter || '无'}\n`;
                    testResults.push({ name: '日志迁移', status: 'fail' });
                }
                
            } catch (error) {
                result += `❌ 测试过程中发生错误: ${error.message}\n`;
                testResults.push({ name: '日志迁移', status: 'fail' });
            }
            
            resultDiv.textContent = result;
        }

        // 测试导出命名
        function testExportNaming() {
            const resultDiv = document.getElementById('exportNamingResult');
            resultDiv.style.display = 'block';
            
            let result = '🔄 测试导出功能命名...\n\n';
            
            try {
                // 模拟导出数据结构
                const mockExportData = {
                    type: "ToolMaster_Category_Export",
                    version: "1.0",
                    exportDate: new Date().toISOString(),
                    categoryPath: "测试分类",
                    metadata: {
                        totalTools: 5,
                        exportedBy: "ToolMaster",
                    },
                    tools: []
                };
                
                result += '✅ 步骤1: 创建模拟导出数据\n';
                result += `   类型标识: ${mockExportData.type}\n`;
                result += `   导出者: ${mockExportData.metadata.exportedBy}\n\n`;
                
                // 检查命名规范
                const hasCorrectType = mockExportData.type === "ToolMaster_Category_Export";
                const hasCorrectExporter = mockExportData.metadata.exportedBy === "ToolMaster";
                
                if (hasCorrectType && hasCorrectExporter) {
                    result += '✅ 步骤2: 导出数据命名验证成功\n';
                    testResults.push({ name: '导出命名', status: 'pass' });
                } else {
                    result += '❌ 步骤2: 导出数据命名验证失败\n';
                    testResults.push({ name: '导出命名', status: 'fail' });
                }
                
                // 模拟文件名生成
                const fileName = `toolmaster-category-测试分类-${new Date().toISOString().split('T')[0]}.json`;
                result += `\n📁 预期文件名: ${fileName}\n`;
                
                if (fileName.startsWith('toolmaster-')) {
                    result += '✅ 步骤3: 文件名前缀验证成功\n';
                } else {
                    result += '❌ 步骤3: 文件名前缀验证失败\n';
                }
                
            } catch (error) {
                result += `❌ 测试过程中发生错误: ${error.message}\n`;
                testResults.push({ name: '导出命名', status: 'fail' });
            }
            
            resultDiv.textContent = result;
        }

        // 测试 localStorage 键名
        function testLocalStorageKeys() {
            const resultDiv = document.getElementById('storageKeysResult');
            resultDiv.style.display = 'block';
            
            let result = '🔄 测试 localStorage 键名...\n\n';
            
            try {
                const expectedKeys = [
                    'toolmaster-tools',
                    'toolmaster-categories', 
                    'toolmaster-search-history',
                    'toolmaster-logs',
                    'toolmaster-sync-queue'
                ];
                
                result += '📋 预期的键名列表:\n';
                expectedKeys.forEach(key => {
                    result += `   - ${key}\n`;
                });
                result += '\n';
                
                // 检查当前 localStorage 中的相关键
                const currentKeys = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith('toolmaster-')) {
                        currentKeys.push(key);
                    }
                }
                
                result += '🔍 当前存在的键名:\n';
                if (currentKeys.length > 0) {
                    currentKeys.forEach(key => {
                        result += `   ✅ ${key}\n`;
                    });
                } else {
                    result += '   (无相关键名)\n';
                }
                result += '\n';
                
                // 检查是否还有旧的键名
                const oldKeys = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith('toolshift-')) {
                        oldKeys.push(key);
                    }
                }
                
                if (oldKeys.length > 0) {
                    result += '⚠️ 发现旧版本键名:\n';
                    oldKeys.forEach(key => {
                        result += `   - ${key}\n`;
                    });
                    testResults.push({ name: 'localStorage键名', status: 'fail' });
                } else {
                    result += '✅ 未发现旧版本键名\n';
                    testResults.push({ name: 'localStorage键名', status: 'pass' });
                }
                
            } catch (error) {
                result += `❌ 测试过程中发生错误: ${error.message}\n`;
                testResults.push({ name: 'localStorage键名', status: 'fail' });
            }
            
            resultDiv.textContent = result;
        }

        // 显示调试信息
        function showDebugInfo() {
            const debugDiv = document.getElementById('debugInfo');
            debugDiv.style.display = 'block';
            
            let info = '🔍 系统调试信息\n\n';
            
            // localStorage 信息
            info += '📦 localStorage 状态:\n';
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && (key.includes('toolmaster') || key.includes('toolshift'))) {
                    const value = localStorage.getItem(key);
                    info += `   ${key}: ${value ? `${value.length} 字符` : '空'}\n`;
                }
            }
            
            info += '\n🌐 浏览器信息:\n';
            info += `   User Agent: ${navigator.userAgent}\n`;
            info += `   时间戳: ${new Date().toISOString()}\n`;
            
            debugDiv.textContent = info;
        }

        // 显示测试总结
        function displayTestSummary() {
            const resultsDiv = document.getElementById('testResults');
            
            let summary = '📊 测试总结\n\n';
            let passCount = 0;
            let totalCount = testResults.length;
            
            testResults.forEach(test => {
                const status = test.status === 'pass' ? '✅' : '❌';
                summary += `${status} ${test.name}: ${test.status === 'pass' ? '通过' : '失败'}\n`;
                if (test.status === 'pass') passCount++;
            });
            
            summary += `\n总计: ${passCount}/${totalCount} 项测试通过\n`;
            
            if (passCount === totalCount) {
                summary += '\n🎉 所有测试通过！命名一致性修改成功。';
                resultsDiv.className = 'test-result success';
            } else {
                summary += '\n⚠️ 部分测试失败，请检查具体项目。';
                resultsDiv.className = 'test-result warning';
            }
            
            resultsDiv.style.display = 'block';
            resultsDiv.textContent = summary;
        }

        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 ToolMaster 命名一致性测试页面已加载');
            console.log('📝 请点击"运行所有测试"按钮开始自动化测试');
        });
    </script>
</body>
</html>
