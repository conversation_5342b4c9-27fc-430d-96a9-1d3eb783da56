# 🔒 安全验证清单

## 📋 部署后必须验证的安全项目

### ✅ **第一步：基础功能验证**

1. **页面访问认证**
   - [ ] 访问主页时显示密码输入界面
   - [ ] 输入错误密码被拒绝
   - [ ] 输入正确密码可以进入应用
   - [ ] 会话超时后需要重新认证

2. **API密钥获取**
   - [ ] 认证成功后可以获取API密钥
   - [ ] API密钥存储在sessionStorage中
   - [ ] 退出登录时API密钥被清除

### 🚫 **第二步：安全漏洞验证**

#### **使用安全测试工具**
1. 打开 `test-api-security.html` 文件
2. 按顺序执行以下测试：

#### **测试1: 无认证访问（必须失败）**
- [ ] `/api/analyze-url` 返回 401/403
- [ ] `/api/deep-search` 返回 401/403  
- [ ] `/api/global-search` 返回 401/403
- [ ] `/api/fetch-content` 返回 401/403

#### **测试2: 错误API密钥（必须失败）**
- [ ] 使用错误密钥访问所有API都返回 401/403
- [ ] 错误信息不泄露敏感信息

#### **测试3: 正确API密钥（必须成功）**
- [ ] 使用正确密钥可以正常调用所有API
- [ ] API返回预期的数据格式

### 🔍 **第三步：手动安全测试**

#### **使用curl命令测试**

```bash
# 测试无认证访问（应该被拒绝）
curl -X POST https://your-domain.com/api/analyze-url \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com"}'

# 测试错误API密钥（应该被拒绝）
curl -X POST https://your-domain.com/api/analyze-url \
  -H "Content-Type: application/json" \
  -H "X-API-Key: wrong-key" \
  -d '{"url": "https://example.com"}'

# 测试正确API密钥（应该成功）
curl -X POST https://your-domain.com/api/analyze-url \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-correct-api-key" \
  -d '{"url": "https://example.com"}'
```

#### **预期结果**
- 无认证: `{"error": "API access key is required", "code": "API_KEY_MISSING"}`
- 错误密钥: `{"error": "Invalid API access key", "code": "API_KEY_INVALID"}`
- 正确密钥: 正常的API响应数据

### 📊 **第四步：功能完整性验证**

1. **工具添加功能**
   - [ ] 快速添加工具时AI分析正常工作
   - [ ] 工具可以正常保存到数据库

2. **搜索功能**
   - [ ] 深度搜索功能正常
   - [ ] 全网搜索功能正常
   - [ ] 搜索结果显示正确

3. **数据同步**
   - [ ] 本地和云端数据同步正常
   - [ ] 离线模式下功能正常

### ⚠️ **第五步：安全配置检查**

#### **环境变量检查**
- [ ] `API_ACCESS_KEY` 已设置且足够复杂
- [ ] `ACCESS_PASSWORDS` 已设置且安全
- [ ] `DEEPSEEK_API_KEY` 已设置且有效
- [ ] `NEXT_PUBLIC_SUPABASE_URL` 已设置
- [ ] `NEXT_PUBLIC_SUPABASE_ANON_KEY` 已设置

#### **API密钥安全性**
- [ ] API密钥长度至少16位
- [ ] API密钥包含字母、数字组合
- [ ] API密钥不包含敏感信息
- [ ] API密钥定期更换

### 🚨 **第六步：监控和告警**

#### **访问日志监控**
- [ ] 检查服务器日志中的API访问记录
- [ ] 监控异常的API调用频率
- [ ] 关注失败的认证尝试

#### **资源使用监控**
- [ ] 监控DeepSeek API使用量
- [ ] 设置API使用量告警
- [ ] 监控Supabase数据库使用情况

### 📝 **验证记录模板**

```
安全验证记录
验证日期: ___________
验证人员: ___________

基础功能验证:
□ 页面认证 - 通过/失败
□ API密钥获取 - 通过/失败

安全测试:
□ 无认证访问被拒绝 - 通过/失败
□ 错误密钥被拒绝 - 通过/失败  
□ 正确密钥可访问 - 通过/失败

功能完整性:
□ 工具添加 - 通过/失败
□ 搜索功能 - 通过/失败
□ 数据同步 - 通过/失败

总体评估: 安全/存在风险
备注: ___________
```

## 🎯 **验证通过标准**

### ✅ **必须全部通过的项目**
1. 所有API路由在无认证时返回401/403
2. 错误API密钥被正确拒绝
3. 正确API密钥可以正常访问
4. 页面认证机制正常工作
5. 会话管理正常工作

### ⚠️ **如果验证失败**
1. 立即停止使用应用
2. 检查环境变量配置
3. 重新部署应用
4. 重新执行验证流程

## 📞 **紧急联系**

如果发现安全漏洞：
1. 立即停止应用访问
2. 更换所有API密钥
3. 检查访问日志
4. 修复安全问题后重新部署

---

**⚠️ 重要提醒：只有通过所有安全验证的应用才能投入生产使用！**
