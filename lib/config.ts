// 访问密码配置
export const ACCESS_PASSWORDS = process.env.ACCESS_PASSWORDS
    ? process.env.ACCESS_PASSWORDS.split(',').map(pwd => pwd.trim())
    : [
        "toolmaster204",
        "admin123456",
        "secure2024"
    ]

// 安全配置
export const SECURITY_CONFIG = {
  // 会话超时时间（毫秒）- 15分钟
  SESSION_TIMEOUT: 15 * 60 * 1000,

  // 密码尝试限制
  MAX_ATTEMPTS_PER_WINDOW: 3,

  // 锁定时间配置（毫秒）
  LOCKOUT_DURATIONS: [
    1 * 60 * 1000,   // 第1次锁定：1分钟
    3 * 60 * 1000,   // 第2次锁定：3分钟
    10 * 60 * 1000,  // 第3次锁定：10分钟
    30 * 60 * 1000,  // 第4次及以后：30分钟
  ],

  // 本地存储键名
  STORAGE_KEYS: {
    AUTH_STATUS: "toolmaster-auth-status",
    AUTH_TIMESTAMP: "toolmaster-auth-timestamp",
    FAILED_ATTEMPTS: "toolmaster-failed-attempts",
    LOCKOUT_UNTIL: "toolmaster-lockout-until",
    LOCKOUT_COUNT: "toolmaster-lockout-count"
  }
}
