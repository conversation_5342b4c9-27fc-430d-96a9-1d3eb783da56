/**
 * API认证工具函数
 * 用于验证API请求的访问权限
 */

import { NextRequest, NextResponse } from 'next/server'

/**
 * 验证API请求是否包含有效的访问密钥
 * @param request - Next.js请求对象
 * @returns 如果验证失败返回错误响应，成功返回null
 */
export function validateApiAccess(request: NextRequest): NextResponse | null {
  // 获取环境变量中的API密钥
  const validApiKey = process.env.API_ACCESS_KEY
  
  // 如果没有配置API密钥，拒绝访问
  if (!validApiKey) {
    console.error('API_ACCESS_KEY environment variable is not configured')
    return NextResponse.json(
      { 
        error: 'API access key not configured',
        code: 'API_KEY_NOT_CONFIGURED'
      },
      { status: 500 }
    )
  }

  // 从请求头中获取API密钥
  const providedApiKey = request.headers.get('X-API-Key') || 
                        request.headers.get('x-api-key') ||
                        request.headers.get('Authorization')?.replace('Bearer ', '')

  // 检查是否提供了API密钥
  if (!providedApiKey) {
    return NextResponse.json(
      { 
        error: 'API access key is required',
        code: 'API_KEY_MISSING',
        message: 'Please provide API key in X-API-Key header or Authorization header'
      },
      { status: 401 }
    )
  }

  // 验证API密钥是否正确
  if (providedApiKey !== validApiKey) {
    console.warn('Invalid API key attempt:', {
      provided: providedApiKey.substring(0, 8) + '...',
      timestamp: new Date().toISOString(),
      ip: request.ip || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown'
    })
    
    return NextResponse.json(
      { 
        error: 'Invalid API access key',
        code: 'API_KEY_INVALID'
      },
      { status: 403 }
    )
  }

  // 验证成功，返回null表示可以继续处理请求
  return null
}

/**
 * 生成随机API密钥的辅助函数（用于开发时生成密钥）
 * @param length - 密钥长度，默认32位
 * @returns 随机生成的API密钥
 */
export function generateApiKey(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 获取当前配置的API密钥（仅用于前端调用）
 * 注意：这个函数只能在认证成功后的客户端使用
 */
export function getApiKey(): string {
  // 在客户端环境中，我们需要从某个安全的地方获取API密钥
  // 这里我们使用一个简单的方案：将API密钥存储在sessionStorage中
  if (typeof window !== 'undefined') {
    return sessionStorage.getItem('api-access-key') || ''
  }
  return ''
}

/**
 * 设置API密钥到客户端存储
 * @param apiKey - API密钥
 */
export function setApiKey(apiKey: string): void {
  if (typeof window !== 'undefined') {
    sessionStorage.setItem('api-access-key', apiKey)
  }
}

/**
 * 清除客户端存储的API密钥
 */
export function clearApiKey(): void {
  if (typeof window !== 'undefined') {
    sessionStorage.removeItem('api-access-key')
  }
}
