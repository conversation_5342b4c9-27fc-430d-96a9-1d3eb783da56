import { SECURITY_CONFIG } from "./config"
import { clearApiKey } from "./api-auth"

/**
 * 检查用户是否已认证且会话未过期
 */
export function isAuthenticated(): boolean {
  if (typeof window === 'undefined') {
    // 服务端渲染时返回 false
    return false
  }

  const authStatus = localStorage.getItem(SECURITY_CONFIG.STORAGE_KEYS.AUTH_STATUS)
  const authTimestamp = localStorage.getItem(SECURITY_CONFIG.STORAGE_KEYS.AUTH_TIMESTAMP)
  
  if (authStatus !== "true" || !authTimestamp) {
    return false
  }

  const lastActivity = parseInt(authTimestamp)
  const now = Date.now()
  
  // 检查是否超时（15分钟）
  if (now - lastActivity > SECURITY_CONFIG.SESSION_TIMEOUT) {
    // 会话超时，清除认证状态
    clearAuthStatus()
    return false
  }

  return true
}

/**
 * 清除认证状态
 */
export function clearAuthStatus(): void {
  if (typeof window === 'undefined') {
    return
  }

  localStorage.removeItem(SECURITY_CONFIG.STORAGE_KEYS.AUTH_STATUS)
  localStorage.removeItem(SECURITY_CONFIG.STORAGE_KEYS.AUTH_TIMESTAMP)
  // 同时清除API密钥
  clearApiKey()
}

/**
 * 更新最后活动时间
 */
export function updateLastActivity(): void {
  if (typeof window === 'undefined') {
    return
  }

  if (isAuthenticated()) {
    localStorage.setItem(SECURITY_CONFIG.STORAGE_KEYS.AUTH_TIMESTAMP, Date.now().toString())
  }
}

/**
 * 设置认证状态
 */
export function setAuthStatus(): void {
  if (typeof window === 'undefined') {
    return
  }

  localStorage.setItem(SECURITY_CONFIG.STORAGE_KEYS.AUTH_STATUS, "true")
  localStorage.setItem(SECURITY_CONFIG.STORAGE_KEYS.AUTH_TIMESTAMP, Date.now().toString())
}
