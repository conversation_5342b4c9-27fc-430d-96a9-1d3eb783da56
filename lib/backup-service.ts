import COS from 'cos-nodejs-sdk-v5'
import { ExtendedDatabase } from './database-extended'

/**
 * 腾讯云COS备份服务
 * 用于自动备份ToolMaster数据到腾讯云COS
 */
export class BackupService {
  private static cos: COS | null = null

  /**
   * 初始化腾讯云COS客户端
   */
  private static initCOS(): COS {
    if (!this.cos) {
      const secretId = process.env.TENCENT_CLOUD_SECRET_ID
      const secretKey = process.env.TENCENT_CLOUD_SECRET_KEY
      const region = process.env.TENCENT_CLOUD_COS_REGION

      if (!secretId || !secretKey || !region) {
        throw new Error('腾讯云COS配置不完整，请检查环境变量')
      }

      this.cos = new COS({
        SecretId: secretId,
        SecretKey: secretKey,
        Region: region,
      })
    }
    return this.cos
  }

  /**
   * 获取备份配置
   */
  private static getBackupConfig() {
    const bucket = process.env.TENCENT_CLOUD_COS_BUCKET
    const region = process.env.TENCENT_CLOUD_COS_REGION
    const backupPath = process.env.TENCENT_CLOUD_COS_BACKUP_PATH || 'backups/toolmaster'

    if (!bucket || !region) {
      throw new Error('腾讯云COS存储桶配置不完整')
    }

    return { bucket, region, backupPath }
  }

  /**
   * 生成备份数据
   */
  private static async generateBackupData(): Promise<{
    data: any
    metadata: any
  }> {
    try {
      // 从数据库获取所有数据
      const { ToolsDatabase, CategoriesDatabase } = await import('./database')
      const tools = await ToolsDatabase.getAll()
      const categories = await CategoriesDatabase.get()

      // 生成备份数据
      const backupData = {
        tools,
        categories,
        version: '1.0.0',
        exportedAt: new Date().toISOString(),
        exportedBy: 'ToolMaster Auto Backup',
        type: 'ToolMaster_Auto_Backup'
      }

      // 生成元数据
      const metadata = {
        timestamp: new Date().toISOString(),
        toolsCount: tools.length,
        categoriesCount: categories.length,
        dataSize: JSON.stringify(backupData).length,
        backupType: 'auto',
        version: '1.0.0'
      }

      return { data: backupData, metadata }
    } catch (error) {
      console.error('生成备份数据失败:', error)
      throw new Error(`生成备份数据失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  /**
   * 上传文件到腾讯云COS
   */
  private static async uploadToCOS(
    key: string,
    body: string,
    contentType: string = 'application/json'
  ): Promise<void> {
    const cos = this.initCOS()
    const { bucket } = this.getBackupConfig()

    return new Promise((resolve, reject) => {
      cos.putObject({
        Bucket: bucket,
        Region: process.env.TENCENT_CLOUD_COS_REGION!,
        Key: key,
        Body: body,
        ContentType: contentType,
        Headers: {
          'x-cos-meta-source': 'toolmaster-auto-backup',
          'x-cos-meta-timestamp': new Date().toISOString()
        }
      }, (err, data) => {
        if (err) {
          console.error('上传到COS失败:', err)
          reject(new Error(`上传失败: ${err.message}`))
        } else {
          console.log('上传到COS成功:', data.Location)
          resolve()
        }
      })
    })
  }

  /**
   * 执行自动备份
   */
  static async performAutoBackup(): Promise<{
    success: boolean
    message: string
    details?: any
  }> {
    const startTime = Date.now()
    let backupDetails: any = {}

    try {
      console.log('开始执行自动备份...')

      // 1. 生成备份数据
      const { data: backupData, metadata } = await this.generateBackupData()
      backupDetails = { ...metadata }

      // 2. 准备文件路径和内容
      const { backupPath } = this.getBackupConfig()
      const dateStr = new Date().toISOString().split('T')[0]
      const timeStr = new Date().toISOString().replace(/[:.]/g, '-').split('T')[1].split('.')[0]
      
      const backupFileName = `toolmaster-backup-${dateStr}.json`
      const logFileName = `backup-log-${dateStr}-${timeStr}.json`
      
      const backupKey = `${backupPath}/daily/${backupFileName}`
      const logKey = `${backupPath}/logs/${logFileName}`

      // 3. 上传备份文件
      await this.uploadToCOS(backupKey, JSON.stringify(backupData, null, 2))

      // 4. 生成并上传日志文件
      const logData = {
        ...metadata,
        backupFileName,
        backupKey,
        executionTime: Date.now() - startTime,
        status: 'success',
        message: '自动备份执行成功'
      }

      await this.uploadToCOS(logKey, JSON.stringify(logData, null, 2))

      // 5. 记录操作日志到数据库
      await ExtendedDatabase.createOperationLog(
        '自动备份到腾讯云COS',
        'backup',
        'cos',
        undefined,
        {
          backupType: 'auto',
          backupFileName,
          backupKey,
          logFileName,
          logKey,
          ...backupDetails,
          executionTime: Date.now() - startTime
        },
        'success'
      )

      const successMessage = `自动备份成功完成，备份了 ${metadata.toolsCount} 个工具和 ${metadata.categoriesCount} 个分类`
      console.log(successMessage)

      return {
        success: true,
        message: successMessage,
        details: {
          ...backupDetails,
          backupFileName,
          logFileName,
          executionTime: Date.now() - startTime
        }
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      console.error('自动备份失败:', errorMessage)

      // 记录错误日志
      try {
        await ExtendedDatabase.createOperationLog(
          '自动备份到腾讯云COS',
          'backup',
          'cos',
          undefined,
          {
            backupType: 'auto',
            ...backupDetails,
            executionTime: Date.now() - startTime,
            error: errorMessage
          },
          'error',
          errorMessage
        )
      } catch (logError) {
        console.error('记录错误日志失败:', logError)
      }

      return {
        success: false,
        message: `自动备份失败: ${errorMessage}`,
        details: {
          ...backupDetails,
          error: errorMessage,
          executionTime: Date.now() - startTime
        }
      }
    }
  }

  /**
   * 测试腾讯云COS连接
   */
  static async testCOSConnection(): Promise<{
    success: boolean
    message: string
    details?: any
  }> {
    try {
      const cos = this.initCOS()
      const { bucket } = this.getBackupConfig()

      return new Promise((resolve) => {
        cos.headBucket({
          Bucket: bucket,
          Region: process.env.TENCENT_CLOUD_COS_REGION!
        }, (err, data) => {
          if (err) {
            resolve({
              success: false,
              message: `COS连接测试失败: ${err.message}`,
              details: { error: err.message }
            })
          } else {
            resolve({
              success: true,
              message: 'COS连接测试成功',
              details: { 
                bucket,
                region: process.env.TENCENT_CLOUD_COS_REGION,
                headers: data.headers
              }
            })
          }
        })
      })
    } catch (error) {
      return {
        success: false,
        message: `COS连接测试异常: ${error instanceof Error ? error.message : '未知错误'}`,
        details: { error: error instanceof Error ? error.message : '未知错误' }
      }
    }
  }

  /**
   * 获取备份历史列表
   */
  static async getBackupHistory(limit: number = 10): Promise<{
    success: boolean
    message: string
    backups?: any[]
  }> {
    try {
      const cos = this.initCOS()
      const { bucket, backupPath } = this.getBackupConfig()

      return new Promise((resolve) => {
        cos.getBucket({
          Bucket: bucket,
          Region: process.env.TENCENT_CLOUD_COS_REGION!,
          Prefix: `${backupPath}/daily/`,
          MaxKeys: limit
        }, (err, data) => {
          if (err) {
            resolve({
              success: false,
              message: `获取备份历史失败: ${err.message}`
            })
          } else {
            const backups = data.Contents?.map(item => ({
              key: item.Key,
              size: item.Size,
              lastModified: item.LastModified,
              etag: item.ETag
            })) || []

            resolve({
              success: true,
              message: `成功获取 ${backups.length} 个备份记录`,
              backups
            })
          }
        })
      })
    } catch (error) {
      return {
        success: false,
        message: `获取备份历史异常: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }
}
