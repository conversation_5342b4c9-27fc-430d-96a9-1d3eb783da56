import { Tool, Category } from './types'

/**
 * 书签导出工具类
 * 用于将工具数据导出为浏览器书签格式（HTML）
 */
export class BookmarkExporter {
  /**
   * 将工具数据导出为浏览器书签格式
   * @param tools 工具数据
   * @param categories 分类数据
   * @returns HTML格式的书签文件内容
   */
  static exportToBookmarks(tools: Tool[], categories: Category[]): string {
    const timestamp = Math.floor(Date.now() / 1000)
    
    // 构建书签HTML结构
    let html = `<!DOCTYPE NETSCAPE-Bookmark-file-1>
<!-- This is an automatically generated file.
     It will be read and overwritten.
     DO NOT EDIT! -->
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=UTF-8">
<TITLE>Bookmarks</TITLE>
<H1>Bookmarks</H1>
<DL><p>
    <DT><H3 ADD_DATE="${timestamp}" LAST_MODIFIED="${timestamp}" PERSONAL_TOOLBAR_FOLDER="true">书签栏</H3>
    <DL><p>
        <DT><H3 ADD_DATE="${timestamp}" LAST_MODIFIED="${timestamp}">ToolMaster 工具箱</H3>
        <DL><p>
`

    // 按分类组织工具
    const toolsByCategory = this.organizeToolsByCategory(tools, categories)
    
    // 生成每个一级分类的书签
    categories.forEach(category => {
      const categoryTools = toolsByCategory[category.id] || {}
      
      // 如果该分类下没有工具，跳过
      if (Object.keys(categoryTools).length === 0) {
        return
      }
      
      html += `            <DT><H3 ADD_DATE="${timestamp}" LAST_MODIFIED="${timestamp}">${this.escapeHtml(category.name)}</H3>\n`
      html += `            <DL><p>\n`
      
      // 生成二级分类
      category.subcategories.forEach(subcategory => {
        const subcategoryTools = categoryTools[subcategory.id] || {}
        
        // 如果该二级分类下没有工具，跳过
        if (Object.keys(subcategoryTools).length === 0) {
          return
        }
        
        html += `                <DT><H3 ADD_DATE="${timestamp}" LAST_MODIFIED="${timestamp}">${this.escapeHtml(subcategory.name)}</H3>\n`
        html += `                <DL><p>\n`
        
        // 生成三级分类
        subcategory.subsubcategories.forEach(subsubcategory => {
          const subsubcategoryTools = subcategoryTools[subsubcategory.id] || []
          
          // 如果该三级分类下没有工具，跳过
          if (subsubcategoryTools.length === 0) {
            return
          }
          
          html += `                    <DT><H3 ADD_DATE="${timestamp}" LAST_MODIFIED="${timestamp}">${this.escapeHtml(subsubcategory.name)}</H3>\n`
          html += `                    <DL><p>\n`
          
          // 生成工具书签
          subsubcategoryTools.forEach(tool => {
            const toolTimestamp = Math.floor(new Date(tool.addedAt).getTime() / 1000)
            html += `                        <DT><A HREF="${this.escapeHtml(tool.url)}" ADD_DATE="${toolTimestamp}">${this.escapeHtml(tool.name)}</A>\n`
          })
          
          html += `                    </DL><p>\n`
        })
        
        html += `                </DL><p>\n`
      })
      
      html += `            </DL><p>\n`
    })

    // 结束HTML结构
    html += `        </DL><p>
    </DL><p>
</DL><p>
`

    return html
  }

  /**
   * 按分类组织工具数据
   * @param tools 工具数据
   * @param categories 分类数据
   * @returns 按分类组织的工具数据
   */
  private static organizeToolsByCategory(tools: Tool[], categories: Category[]) {
    const organized: Record<string, Record<string, Record<string, Tool[]>>> = {}
    
    // 初始化分类结构
    categories.forEach(category => {
      organized[category.id] = {}
      category.subcategories.forEach(subcategory => {
        organized[category.id][subcategory.id] = {}
        subcategory.subsubcategories.forEach(subsubcategory => {
          organized[category.id][subcategory.id][subsubcategory.id] = []
        })
      })
    })
    
    // 将工具分配到对应分类
    tools.forEach(tool => {
      if (organized[tool.category] && 
          organized[tool.category][tool.subcategory] && 
          organized[tool.category][tool.subcategory][tool.subsubcategory]) {
        organized[tool.category][tool.subcategory][tool.subsubcategory].push(tool)
      }
    })
    
    return organized
  }

  /**
   * HTML转义
   * @param text 需要转义的文本
   * @returns 转义后的文本
   */
  private static escapeHtml(text: string): string {
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }

  /**
   * 下载书签文件
   * @param tools 工具数据
   * @param categories 分类数据
   * @param filename 文件名（可选）
   */
  static downloadBookmarks(tools: Tool[], categories: Category[], filename?: string): void {
    const bookmarkHtml = this.exportToBookmarks(tools, categories)
    const blob = new Blob([bookmarkHtml], { type: 'text/html;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    
    const link = document.createElement('a')
    link.href = url
    link.download = filename || `toolmaster-bookmarks-${new Date().toISOString().split('T')[0]}.html`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  /**
   * 获取导出统计信息
   * @param tools 工具数据
   * @param categories 分类数据
   * @returns 导出统计信息
   */
  static getExportStats(tools: Tool[], categories: Category[]) {
    const toolsByCategory = this.organizeToolsByCategory(tools, categories)
    
    let totalCategories = 0
    let totalSubcategories = 0
    let totalSubsubcategories = 0
    let totalTools = 0
    
    categories.forEach(category => {
      const categoryTools = toolsByCategory[category.id] || {}
      const hasTools = Object.values(categoryTools).some(subcategoryTools => 
        Object.values(subcategoryTools).some(tools => tools.length > 0)
      )
      
      if (hasTools) {
        totalCategories++
        
        category.subcategories.forEach(subcategory => {
          const subcategoryTools = categoryTools[subcategory.id] || {}
          const hasSubcategoryTools = Object.values(subcategoryTools).some(tools => tools.length > 0)
          
          if (hasSubcategoryTools) {
            totalSubcategories++
            
            subcategory.subsubcategories.forEach(subsubcategory => {
              const subsubcategoryTools = subcategoryTools[subsubcategory.id] || []
              
              if (subsubcategoryTools.length > 0) {
                totalSubsubcategories++
                totalTools += subsubcategoryTools.length
              }
            })
          }
        })
      }
    })
    
    return {
      totalCategories,
      totalSubcategories,
      totalSubsubcategories,
      totalTools
    }
  }
}
