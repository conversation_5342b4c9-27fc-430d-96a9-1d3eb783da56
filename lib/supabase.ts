import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  realtime: {
    params: {
      eventsPerSecond: 10,
    },
  },
})

// 数据库类型定义
export interface DatabaseTool {
  id: string
  name: string
  url: string
  description: string
  tags: string[]
  category: string
  subcategory: string
  subsubcategory: string
  added_at: string
  sensitive: boolean
  created_at: string
  updated_at: string
}

export interface DatabaseCategory {
  id: string
  data: any[] // JSONB 类型
  updated_at: string
}
