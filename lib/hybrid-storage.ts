import { Tool, Category } from './types'
import { ToolsDatabase, CategoriesDatabase, DataMigration } from './database'
import { realtimeManager } from './realtime'

// 混合存储管理器 - 结合 localStorage 和 Supabase
export class HybridStorageManager {
  private static instance: HybridStorageManager
  private isOnline = true
  private syncInProgress = false

  // 本地缓存
  private toolsCache: Tool[] = []
  private categoriesCache: Category[] = []

  // 回调函数
  private onToolsChange?: (tools: Tool[]) => void
  private onCategoriesChange?: (categories: Category[]) => void
  private onSyncStatusChange?: (syncing: boolean) => void

  private constructor() {
    // 监听网络状态
    if (typeof window !== 'undefined') {
      window.addEventListener('online', () => {
        this.isOnline = true
        this.syncWithCloud()
      })

      window.addEventListener('offline', () => {
        this.isOnline = false
      })
    }
  }

  static getInstance(): HybridStorageManager {
    if (!HybridStorageManager.instance) {
      HybridStorageManager.instance = new HybridStorageManager()
    }
    return HybridStorageManager.instance
  }

  // 初始化存储管理器
  async initialize(callbacks: {
    onToolsChange?: (tools: Tool[]) => void
    onCategoriesChange?: (categories: Category[]) => void
    onSyncStatusChange?: (syncing: boolean) => void
  }) {
    this.onToolsChange = callbacks.onToolsChange
    this.onCategoriesChange = callbacks.onCategoriesChange
    this.onSyncStatusChange = callbacks.onSyncStatusChange

    // 检查是否需要数据迁移
    if (await DataMigration.shouldMigrate()) {
      console.log('检测到本地数据，开始迁移...')
      await DataMigration.migrateFromLocalStorage()
    }

    // 初始化实时连接
    await realtimeManager.initialize({
      onToolsChange: (tools) => {
        this.toolsCache = tools
        this.saveToLocalStorage('tools', tools)
        this.onToolsChange?.(tools)
      },
      onCategoriesChange: (categories) => {
        this.categoriesCache = categories
        this.saveToLocalStorage('categories', categories)
        this.onCategoriesChange?.(categories)
      },
      onConnectionChange: (connected) => {
        this.isOnline = connected
        if (connected) {
          this.syncWithCloud()
        }
      }
    })

    // 加载初始数据
    await this.loadInitialData()
  }

  // 加载初始数据
  private async loadInitialData() {
    try {
      // 优先从云端加载
      if (this.isOnline) {
        const [tools, categories] = await Promise.all([
          ToolsDatabase.getAll(),
          CategoriesDatabase.get()
        ])

        this.toolsCache = tools
        this.categoriesCache = categories

        // 同步到本地存储
        this.saveToLocalStorage('tools', tools)
        this.saveToLocalStorage('categories', categories)
      } else {
        // 离线时从本地存储加载
        this.toolsCache = this.loadFromLocalStorage('tools')
        this.categoriesCache = this.loadFromLocalStorage('categories')
      }

      // 通知回调
      this.onToolsChange?.(this.toolsCache)
      this.onCategoriesChange?.(this.categoriesCache)
    } catch (error) {
      console.error('加载初始数据失败:', error)

      // 降级到本地存储
      this.toolsCache = this.loadFromLocalStorage('tools')
      this.categoriesCache = this.loadFromLocalStorage('categories')

      this.onToolsChange?.(this.toolsCache)
      this.onCategoriesChange?.(this.categoriesCache)
    }
  }

  // 获取工具数据
  getTools(): Tool[] {
    return this.toolsCache
  }

  // 获取分类数据
  getCategories(): Category[] {
    return this.categoriesCache
  }

  // 添加工具
  async addTool(tool: Omit<Tool, 'id' | 'addedAt'>): Promise<Tool | null> {
    try {
      if (this.isOnline) {
        // 在线时直接添加到云端
        const newTool = await ToolsDatabase.add(tool)
        if (newTool) {
          // 实时监听会自动更新缓存
          return newTool
        }
      } else {
        // 离线时添加到本地缓存
        const newTool: Tool = {
          ...tool,
          id: Date.now().toString(),
          addedAt: new Date().toISOString()
        }

        this.toolsCache.unshift(newTool)
        this.saveToLocalStorage('tools', this.toolsCache)
        this.onToolsChange?.(this.toolsCache)

        // 标记需要同步
        this.markForSync('tools', newTool)

        return newTool
      }
    } catch (error) {
      console.error('添加工具失败:', error)
    }

    return null
  }

  // 删除工具
  async deleteTool(id: string): Promise<boolean> {
    try {
      if (this.isOnline) {
        // 在线时直接从云端删除
        const success = await ToolsDatabase.delete(id)
        if (success) {
          // 实时监听会自动更新缓存
          return true
        }
      } else {
        // 离线时从本地缓存删除
        this.toolsCache = this.toolsCache.filter(tool => tool.id !== id)
        this.saveToLocalStorage('tools', this.toolsCache)
        this.onToolsChange?.(this.toolsCache)

        // 标记需要同步删除
        this.markForSync('delete', { id })

        return true
      }
    } catch (error) {
      console.error('删除工具失败:', error)
    }

    return false
  }

  // 更新分类
  async updateCategories(categories: Category[]): Promise<boolean> {
    try {
      if (this.isOnline) {
        // 在线时直接更新云端
        const success = await CategoriesDatabase.update(categories)
        if (success) {
          // 实时监听会自动更新缓存
          return true
        }
      } else {
        // 离线时更新本地缓存
        this.categoriesCache = categories
        this.saveToLocalStorage('categories', categories)
        this.onCategoriesChange?.(categories)

        // 标记需要同步
        this.markForSync('categories', categories)

        return true
      }
    } catch (error) {
      console.error('更新分类失败:', error)
    }

    return false
  }

  // 本地存储操作
  private saveToLocalStorage(type: 'tools' | 'categories', data: any) {
    try {
      const key = type === 'tools' ? 'toolmaster-tools' : 'toolmaster-categories'
      localStorage.setItem(key, JSON.stringify(data))
    } catch (error) {
      console.error('保存到本地存储失败:', error)
    }
  }

  private loadFromLocalStorage(type: 'tools' | 'categories'): any[] {
    try {
      const key = type === 'tools' ? 'toolmaster-tools' : 'toolmaster-categories'
      return JSON.parse(localStorage.getItem(key) || '[]')
    } catch (error) {
      console.error('从本地存储加载失败:', error)
      return []
    }
  }

  // 标记需要同步的数据
  private markForSync(type: string, data: any) {
    try {
      const syncQueue = JSON.parse(localStorage.getItem('toolmaster-sync-queue') || '[]')
      syncQueue.push({
        type,
        data,
        timestamp: Date.now()
      })
      localStorage.setItem('toolmaster-sync-queue', JSON.stringify(syncQueue))
    } catch (error) {
      console.error('标记同步失败:', error)
    }
  }

  // 与云端同步
  private async syncWithCloud() {
    if (this.syncInProgress || !this.isOnline) return

    this.syncInProgress = true
    this.onSyncStatusChange?.(true)

    try {
      // 处理同步队列
      const syncQueue = JSON.parse(localStorage.getItem('toolmaster-sync-queue') || '[]')

      for (const item of syncQueue) {
        try {
          switch (item.type) {
            case 'tools':
              await ToolsDatabase.add(item.data)
              break
            case 'categories':
              await CategoriesDatabase.update(item.data)
              break
            case 'delete':
              await ToolsDatabase.delete(item.data.id)
              break
          }
        } catch (error) {
          console.error('同步项目失败:', item, error)
        }
      }

      // 清空同步队列
      localStorage.removeItem('toolmaster-sync-queue')

      console.log('云端同步完成')
    } catch (error) {
      console.error('云端同步失败:', error)
    } finally {
      this.syncInProgress = false
      this.onSyncStatusChange?.(false)
    }
  }

  // 获取同步状态
  getSyncStatus(): { isOnline: boolean; syncing: boolean } {
    return {
      isOnline: this.isOnline,
      syncing: this.syncInProgress
    }
  }

  // 手动触发同步
  async manualSync() {
    await this.syncWithCloud()
  }
}

// 导出单例实例
export const hybridStorage = HybridStorageManager.getInstance()
