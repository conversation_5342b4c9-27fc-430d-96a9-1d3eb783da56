import { Tool, Category } from './types'
import { ExtendedDatabase } from './database-extended'

export interface StatisticsData {
  overview: {
    totalTools: number
    totalCategories: number
    todayAdded: number
    yesterdayAdded: number
    thisMonthAdded: number
    thisWeekAdded: number
    averagePerDay: number
    growthRate: number
  }
  categories: {
    name: string
    count: number
    percentage: number
    subcategories: {
      name: string
      count: number
      percentage: number
    }[]
  }[]
  tags: {
    name: string
    count: number
    percentage: number
  }[]
  timeline: {
    date: string
    count: number
    cumulative: number
  }[]
  trends: {
    dailyGrowth: number[]
    weeklyGrowth: number[]
    monthlyGrowth: number[]
    popularCategories: string[]
    emergingTags: string[]
  }
  quality: {
    withDescription: number
    withTags: number
    averageTagsPerTool: number
    sensitiveTools: number
    duplicateUrls: number
    invalidUrls: number
  }
}

export class StatisticsService {
  
  // 计算完整统计数据
  static async calculateStatistics(tools: Tool[], categories: Category[]): Promise<StatisticsData> {
    const now = new Date()
    const today = now.toISOString().split('T')[0]
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    const thisWeekStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    const thisMonth = now.toISOString().substring(0, 7)
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1).toISOString().substring(0, 7)

    // 基础统计
    const totalTools = tools.length
    const totalCategories = categories.length

    // 时间统计
    const todayAdded = tools.filter(tool => tool.addedAt.startsWith(today)).length
    const yesterdayAdded = tools.filter(tool => tool.addedAt.startsWith(yesterday)).length
    const thisWeekAdded = tools.filter(tool => tool.addedAt >= thisWeekStart).length
    const thisMonthAdded = tools.filter(tool => tool.addedAt.startsWith(thisMonth)).length
    const lastMonthAdded = tools.filter(tool => tool.addedAt.startsWith(lastMonth)).length

    // 增长率计算
    const growthRate = lastMonthAdded > 0 ? ((thisMonthAdded - lastMonthAdded) / lastMonthAdded * 100) : 0
    const averagePerDay = totalTools > 0 ? totalTools / this.getDaysSinceFirstTool(tools) : 0

    // 分类统计
    const categoryStats = this.calculateCategoryStatistics(tools, categories)

    // 标签统计
    const tagStats = this.calculateTagStatistics(tools)

    // 时间线统计
    const timeline = this.calculateTimeline(tools)

    // 趋势分析
    const trends = this.calculateTrends(tools)

    // 质量统计
    const quality = this.calculateQualityStatistics(tools)

    const statistics: StatisticsData = {
      overview: {
        totalTools,
        totalCategories,
        todayAdded,
        yesterdayAdded,
        thisMonthAdded,
        thisWeekAdded,
        averagePerDay: Math.round(averagePerDay * 100) / 100,
        growthRate: Math.round(growthRate * 100) / 100
      },
      categories: categoryStats,
      tags: tagStats,
      timeline,
      trends,
      quality
    }

    // 缓存统计数据
    await ExtendedDatabase.setDataStatistics(
      'daily',
      today,
      statistics,
      new Date(now.getTime() + 60 * 60 * 1000).toISOString() // 1小时后过期
    )

    return statistics
  }

  // 计算分类统计
  private static calculateCategoryStatistics(tools: Tool[], categories: Category[]) {
    const categoryMap = new Map<string, { name: string; count: number; subcategories: Map<string, number> }>()
    
    // 初始化分类映射
    categories.forEach(category => {
      categoryMap.set(category.id, {
        name: category.name,
        count: 0,
        subcategories: new Map()
      })
    })

    // 统计工具分布
    tools.forEach(tool => {
      const categoryData = categoryMap.get(tool.category)
      if (categoryData) {
        categoryData.count++
        
        if (tool.subcategory) {
          const currentCount = categoryData.subcategories.get(tool.subcategory) || 0
          categoryData.subcategories.set(tool.subcategory, currentCount + 1)
        }
      }
    })

    // 转换为结果格式
    return Array.from(categoryMap.entries())
      .map(([categoryId, data]) => {
        const category = categories.find(c => c.id === categoryId)
        const subcategories = Array.from(data.subcategories.entries())
          .map(([subId, count]) => {
            const subcategory = category?.subcategories.find(s => s.id === subId)
            return {
              name: subcategory?.name || subId,
              count,
              percentage: Math.round((count / data.count) * 100 * 100) / 100
            }
          })
          .sort((a, b) => b.count - a.count)

        return {
          name: data.name,
          count: data.count,
          percentage: Math.round((data.count / tools.length) * 100 * 100) / 100,
          subcategories
        }
      })
      .sort((a, b) => b.count - a.count)
  }

  // 计算标签统计
  private static calculateTagStatistics(tools: Tool[]) {
    const tagMap = new Map<string, number>()
    
    tools.forEach(tool => {
      tool.tags.forEach(tag => {
        tagMap.set(tag, (tagMap.get(tag) || 0) + 1)
      })
    })

    return Array.from(tagMap.entries())
      .map(([name, count]) => ({
        name,
        count,
        percentage: Math.round((count / tools.length) * 100 * 100) / 100
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 50) // 只返回前50个标签
  }

  // 计算时间线
  private static calculateTimeline(tools: Tool[]) {
    const dateMap = new Map<string, number>()
    
    tools.forEach(tool => {
      const date = tool.addedAt.split('T')[0]
      dateMap.set(date, (dateMap.get(date) || 0) + 1)
    })

    const sortedDates = Array.from(dateMap.keys()).sort()
    let cumulative = 0

    return sortedDates.map(date => {
      const count = dateMap.get(date) || 0
      cumulative += count
      return {
        date,
        count,
        cumulative
      }
    })
  }

  // 计算趋势
  private static calculateTrends(tools: Tool[]) {
    const now = new Date()
    const dailyGrowth: number[] = []
    const weeklyGrowth: number[] = []
    const monthlyGrowth: number[] = []

    // 计算最近30天的每日增长
    for (let i = 29; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      const count = tools.filter(tool => tool.addedAt.startsWith(date)).length
      dailyGrowth.push(count)
    }

    // 计算最近12周的每周增长
    for (let i = 11; i >= 0; i--) {
      const weekStart = new Date(now.getTime() - i * 7 * 24 * 60 * 60 * 1000)
      const weekEnd = new Date(weekStart.getTime() + 7 * 24 * 60 * 60 * 1000)
      const count = tools.filter(tool => {
        const toolDate = new Date(tool.addedAt)
        return toolDate >= weekStart && toolDate < weekEnd
      }).length
      weeklyGrowth.push(count)
    }

    // 计算最近12个月的每月增长
    for (let i = 11; i >= 0; i--) {
      const monthDate = new Date(now.getFullYear(), now.getMonth() - i, 1)
      const monthStr = monthDate.toISOString().substring(0, 7)
      const count = tools.filter(tool => tool.addedAt.startsWith(monthStr)).length
      monthlyGrowth.push(count)
    }

    // 热门分类（最近30天）
    const recentTools = tools.filter(tool => {
      const toolDate = new Date(tool.addedAt)
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      return toolDate >= thirtyDaysAgo
    })

    const categoryCount = new Map<string, number>()
    recentTools.forEach(tool => {
      categoryCount.set(tool.category, (categoryCount.get(tool.category) || 0) + 1)
    })

    const popularCategories = Array.from(categoryCount.entries())
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([category]) => category)

    // 新兴标签（最近7天出现的新标签）
    const recentWeekTools = tools.filter(tool => {
      const toolDate = new Date(tool.addedAt)
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      return toolDate >= weekAgo
    })

    const oldTags = new Set(
      tools.filter(tool => {
        const toolDate = new Date(tool.addedAt)
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        return toolDate < weekAgo
      }).flatMap(tool => tool.tags)
    )

    const newTags = new Set<string>()
    recentWeekTools.forEach(tool => {
      tool.tags.forEach(tag => {
        if (!oldTags.has(tag)) {
          newTags.add(tag)
        }
      })
    })

    const emergingTags = Array.from(newTags).slice(0, 10)

    return {
      dailyGrowth,
      weeklyGrowth,
      monthlyGrowth,
      popularCategories,
      emergingTags
    }
  }

  // 计算质量统计
  private static calculateQualityStatistics(tools: Tool[]) {
    const withDescription = tools.filter(tool => tool.description && tool.description.trim().length > 0).length
    const withTags = tools.filter(tool => tool.tags && tool.tags.length > 0).length
    const totalTags = tools.reduce((sum, tool) => sum + (tool.tags?.length || 0), 0)
    const averageTagsPerTool = tools.length > 0 ? totalTags / tools.length : 0
    const sensitiveTools = tools.filter(tool => tool.sensitive).length

    // 检查重复URL
    const urlMap = new Map<string, number>()
    tools.forEach(tool => {
      if (tool.url) {
        urlMap.set(tool.url, (urlMap.get(tool.url) || 0) + 1)
      }
    })
    const duplicateUrls = Array.from(urlMap.values()).filter(count => count > 1).length

    // 检查无效URL（简单检查）
    const invalidUrls = tools.filter(tool => {
      if (!tool.url) return true
      try {
        new URL(tool.url)
        return false
      } catch {
        return true
      }
    }).length

    return {
      withDescription,
      withTags,
      averageTagsPerTool: Math.round(averageTagsPerTool * 100) / 100,
      sensitiveTools,
      duplicateUrls,
      invalidUrls
    }
  }

  // 获取第一个工具添加以来的天数
  private static getDaysSinceFirstTool(tools: Tool[]): number {
    if (tools.length === 0) return 1

    const firstToolDate = new Date(Math.min(...tools.map(tool => new Date(tool.addedAt).getTime())))
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - firstToolDate.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    return Math.max(diffDays, 1) // 至少1天
  }

  // 获取缓存的统计数据
  static async getCachedStatistics(): Promise<StatisticsData | null> {
    const today = new Date().toISOString().split('T')[0]
    const cached = await ExtendedDatabase.getDataStatistics('daily', today)
    
    if (cached && cached.expires_at && new Date(cached.expires_at) > new Date()) {
      return cached.stat_value as StatisticsData
    }
    
    return null
  }
}
