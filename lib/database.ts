import { supabase, DatabaseTool, DatabaseCategory } from './supabase'
import { Tool, Category } from './types'

// 工具数据操作
export class ToolsDatabase {
  // 获取所有工具
  static async getAll(): Promise<Tool[]> {
    try {
      const { data, error } = await supabase
        .from('tools')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error

      return data.map(this.mapDatabaseToTool)
    } catch (error) {
      console.error('获取工具数据失败:', error)
      return []
    }
  }

  // 添加工具
  static async add(tool: Omit<Tool, 'id' | 'addedAt'>): Promise<Tool | null> {
    try {
      const { data, error } = await supabase
        .from('tools')
        .insert([{
          name: tool.name,
          url: tool.url,
          description: tool.description,
          tags: tool.tags,
          category: tool.category,
          subcategory: tool.subcategory,
          subsubcategory: tool.subsubcategory,
          sensitive: tool.sensitive
        }])
        .select()
        .single()

      if (error) throw error

      return this.mapDatabaseToTool(data)
    } catch (error) {
      console.error('添加工具失败:', error)
      return null
    }
  }

  // 删除工具
  static async delete(id: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('tools')
        .delete()
        .eq('id', id)

      if (error) throw error
      return true
    } catch (error) {
      console.error('删除工具失败:', error)
      return false
    }
  }

  // 数据库格式转换为应用格式
  private static mapDatabaseToTool(dbTool: DatabaseTool): Tool {
    return {
      id: dbTool.id,
      name: dbTool.name,
      url: dbTool.url,
      description: dbTool.description,
      tags: dbTool.tags,
      category: dbTool.category,
      subcategory: dbTool.subcategory,
      subsubcategory: dbTool.subsubcategory,
      addedAt: dbTool.added_at,
      sensitive: dbTool.sensitive
    }
  }
}

// 分类数据操作
export class CategoriesDatabase {
  // 获取分类数据
  static async get(): Promise<Category[]> {
    try {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .limit(1)
        .single()

      if (error) throw error

      return data.data as Category[]
    } catch (error) {
      console.error('获取分类数据失败:', error)
      return []
    }
  }

  // 更新分类数据
  static async update(categories: Category[]): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('categories')
        .update({ data: categories })
        .eq('id', (await this.getCategoryId()))

      if (error) throw error
      return true
    } catch (error) {
      console.error('更新分类数据失败:', error)
      return false
    }
  }

  // 获取分类记录ID
  private static async getCategoryId(): Promise<string> {
    const { data } = await supabase
      .from('categories')
      .select('id')
      .limit(1)
      .single()

    return data?.id || ''
  }
}

// 数据迁移工具
export class DataMigration {
  // 从 localStorage 迁移数据到 Supabase
  static async migrateFromLocalStorage(): Promise<boolean> {
    try {
      // 获取 localStorage 数据
      const localTools = JSON.parse(localStorage.getItem('toolmaster-tools') || '[]')
      const localCategories = JSON.parse(localStorage.getItem('toolmaster-categories') || '[]')

      // 迁移工具数据
      if (localTools.length > 0) {
        const { error: toolsError } = await supabase
          .from('tools')
          .insert(localTools.map((tool: Tool) => ({
            name: tool.name,
            url: tool.url,
            description: tool.description,
            tags: tool.tags,
            category: tool.category,
            subcategory: tool.subcategory,
            subsubcategory: tool.subsubcategory,
            sensitive: tool.sensitive,
            added_at: tool.addedAt
          })))

        if (toolsError) throw toolsError
      }

      // 迁移分类数据
      if (localCategories.length > 0) {
        await CategoriesDatabase.update(localCategories)
      }

      console.log('数据迁移成功')
      return true
    } catch (error) {
      console.error('数据迁移失败:', error)
      return false
    }
  }

  // 检查是否需要迁移
  static async shouldMigrate(): Promise<boolean> {
    try {
      // 检查 Supabase 中是否已有数据
      const { data: tools } = await supabase
        .from('tools')
        .select('id')
        .limit(1)

      // 检查 localStorage 中是否有数据
      const localTools = JSON.parse(localStorage.getItem('toolmaster-tools') || '[]')

      // 如果 Supabase 没有数据但 localStorage 有数据，则需要迁移
      return (!tools || tools.length === 0) && localTools.length > 0
    } catch (error) {
      console.error('检查迁移状态失败:', error)
      return false
    }
  }
}
