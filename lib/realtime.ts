import { supabase } from './supabase'
import { Tool, Category } from './types'
import { ToolsDatabase, CategoriesDatabase } from './database'

export type RealtimeEventType = 'INSERT' | 'UPDATE' | 'DELETE'

export interface RealtimeEvent {
  eventType: RealtimeEventType
  new?: any
  old?: any
  table: string
}

export class RealtimeManager {
  private static instance: RealtimeManager
  private toolsChannel: any = null
  private categoriesChannel: any = null
  private isConnected = false

  // 回调函数
  private onToolsChange?: (tools: Tool[]) => void
  private onCategoriesChange?: (categories: Category[]) => void
  private onConnectionChange?: (connected: boolean) => void

  private constructor() {}

  static getInstance(): RealtimeManager {
    if (!RealtimeManager.instance) {
      RealtimeManager.instance = new RealtimeManager()
    }
    return RealtimeManager.instance
  }

  // 初始化实时连接
  async initialize(callbacks: {
    onToolsChange?: (tools: Tool[]) => void
    onCategoriesChange?: (categories: Category[]) => void
    onConnectionChange?: (connected: boolean) => void
  }) {
    this.onToolsChange = callbacks.onToolsChange
    this.onCategoriesChange = callbacks.onCategoriesChange
    this.onConnectionChange = callbacks.onConnectionChange

    await this.setupToolsChannel()
    await this.setupCategoriesChannel()
  }

  // 设置工具表实时监听
  private async setupToolsChannel() {
    this.toolsChannel = supabase
      .channel('tools-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'tools'
        },
        async (payload) => {
          console.log('工具数据变更:', payload)
          
          // 重新获取所有工具数据
          const tools = await ToolsDatabase.getAll()
          this.onToolsChange?.(tools)
        }
      )
      .on('system', {}, (status) => {
        console.log('工具频道状态:', status)
        this.updateConnectionStatus()
      })
      .subscribe((status) => {
        console.log('工具频道订阅状态:', status)
        this.updateConnectionStatus()
      })
  }

  // 设置分类表实时监听
  private async setupCategoriesChannel() {
    this.categoriesChannel = supabase
      .channel('categories-changes')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'categories'
        },
        async (payload) => {
          console.log('分类数据变更:', payload)
          
          // 重新获取分类数据
          const categories = await CategoriesDatabase.get()
          this.onCategoriesChange?.(categories)
        }
      )
      .on('system', {}, (status) => {
        console.log('分类频道状态:', status)
        this.updateConnectionStatus()
      })
      .subscribe((status) => {
        console.log('分类频道订阅状态:', status)
        this.updateConnectionStatus()
      })
  }

  // 更新连接状态
  private updateConnectionStatus() {
    const wasConnected = this.isConnected
    
    // 检查两个频道的状态
    const toolsConnected = this.toolsChannel?.state === 'joined'
    const categoriesConnected = this.categoriesChannel?.state === 'joined'
    
    this.isConnected = toolsConnected && categoriesConnected

    // 如果状态发生变化，通知回调
    if (wasConnected !== this.isConnected) {
      console.log('连接状态变更:', this.isConnected ? '已连接' : '已断开')
      this.onConnectionChange?.(this.isConnected)
    }
  }

  // 获取连接状态
  getConnectionStatus(): boolean {
    return this.isConnected
  }

  // 断开连接
  async disconnect() {
    if (this.toolsChannel) {
      await supabase.removeChannel(this.toolsChannel)
      this.toolsChannel = null
    }

    if (this.categoriesChannel) {
      await supabase.removeChannel(this.categoriesChannel)
      this.categoriesChannel = null
    }

    this.isConnected = false
    this.onConnectionChange?.(false)
  }

  // 重新连接
  async reconnect() {
    await this.disconnect()
    await this.setupToolsChannel()
    await this.setupCategoriesChannel()
  }
}

// 导出单例实例
export const realtimeManager = RealtimeManager.getInstance()
