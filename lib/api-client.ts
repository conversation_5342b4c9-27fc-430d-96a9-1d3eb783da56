/**
 * API客户端工具函数
 * 统一处理所有API调用，自动添加认证头
 */

import { getApiKey } from './api-auth'

/**
 * 创建带认证的fetch请求
 * @param url - 请求URL
 * @param options - fetch选项
 * @returns Promise<Response>
 */
export async function authenticatedFetch(url: string, options: RequestInit = {}): Promise<Response> {
  const apiKey = getApiKey()
  
  if (!apiKey) {
    throw new Error('API key not found. Please re-authenticate.')
  }

  // 合并请求头，添加API密钥
  const headers = {
    'Content-Type': 'application/json',
    'X-API-Key': apiKey,
    ...options.headers,
  }

  return fetch(url, {
    ...options,
    headers,
  })
}

/**
 * 分析URL的API调用
 * @param url - 要分析的URL
 * @returns Promise<any>
 */
export async function analyzeUrl(url: string): Promise<any> {
  const response = await authenticatedFetch('/api/analyze-url', {
    method: 'POST',
    body: JSON.stringify({ url }),
  })

  if (!response.ok) {
    const error = await response.json().catch(() => ({ error: 'Unknown error' }))
    throw new Error(error.error || `HTTP ${response.status}`)
  }

  return response.json()
}

/**
 * 深度搜索API调用
 * @param query - 搜索查询
 * @param tools - 工具列表
 * @returns Promise<any>
 */
export async function deepSearch(query: string, tools: any[]): Promise<any> {
  const response = await authenticatedFetch('/api/deep-search', {
    method: 'POST',
    body: JSON.stringify({ query, tools }),
  })

  if (!response.ok) {
    const error = await response.json().catch(() => ({ error: 'Unknown error' }))
    throw new Error(error.error || `HTTP ${response.status}`)
  }

  return response.json()
}

/**
 * 全网搜索API调用
 * @param query - 搜索查询
 * @returns Promise<any>
 */
export async function globalSearch(query: string): Promise<any> {
  const response = await authenticatedFetch('/api/global-search', {
    method: 'POST',
    body: JSON.stringify({ query }),
  })

  if (!response.ok) {
    const error = await response.json().catch(() => ({ error: 'Unknown error' }))
    throw new Error(error.error || `HTTP ${response.status}`)
  }

  return response.json()
}

/**
 * 获取内容API调用
 * @param url - 要获取内容的URL
 * @returns Promise<any>
 */
export async function fetchContent(url: string): Promise<any> {
  const response = await authenticatedFetch(`/api/fetch-content?url=${encodeURIComponent(url)}`, {
    method: 'GET',
  })

  if (!response.ok) {
    const error = await response.json().catch(() => ({ error: 'Unknown error' }))
    throw new Error(error.error || `HTTP ${response.status}`)
  }

  return response.json()
}
