// 应用核心类型定义

export interface Tool {
  id: string
  name: string
  url: string
  description: string
  tags: string[]
  category: string
  subcategory: string
  subsubcategory: string
  addedAt: string // ISO string date
  sensitive: boolean
}

export interface Category {
  id: string
  name: string
  subcategories: {
    id: string
    name: string
    subsubcategories: {
      id: string
      name: string
    }[]
  }[]
}

// 深度搜索相关类型
export interface DeepSearchResult {
  searchSummary: string
  recommendedTools: Array<{
    id: string
    relevanceScore: number
    recommendReason: string
  }>
  searchInsights: string
}

// AI分析结果类型
export interface AIAnalysisResult {
  name: string
  description: string
  tags: string[]
  category: string
  subcategory: string
  subsubcategory: string
  confidence?: number
}

// 批量导入相关类型
export interface ImportTask {
  id: string
  taskName: string
  fileName?: string
  fileSize?: number
  fileType?: string
  contentPreview?: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  createdAt: string
  completedAt?: string
}

export interface ImportTaskItem {
  id: string
  taskId: string
  itemIndex: number
  originalData: any
  processedData?: any
  toolId?: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  errorMessage?: string
  aiAnalysisResult?: AIAnalysisResult
  processingTimeMs?: number
}

// 清理服务相关类型
export interface CleanupResult {
  operationType: string
  originalCount: number
  processedCount: number
  removedCount: number
  details: any
  removedItems?: Tool[]
  mergedItems?: { original: Tool[], merged: Tool }[]
}

// 统计数据类型
export interface StatisticsData {
  overview: {
    totalTools: number
    totalCategories: number
    todayAdded: number
    yesterdayAdded: number
    thisMonthAdded: number
    thisWeekAdded: number
    averagePerDay: number
    growthRate: number
  }
  categories: {
    name: string
    count: number
    percentage: number
    subcategories: {
      name: string
      count: number
      percentage: number
    }[]
  }[]
  tags: {
    name: string
    count: number
    percentage: number
  }[]
  timeline: {
    date: string
    count: number
    cumulative: number
  }[]
  trends: {
    dailyGrowth: number[]
    weeklyGrowth: number[]
    monthlyGrowth: number[]
    popularCategories: string[]
    emergingTags: string[]
  }
  quality: {
    withDescription: number
    withTags: number
    averageTagsPerTool: number
    sensitiveTools: number
    duplicateUrls: number
    invalidUrls: number
  }
}
