"use client"

import type React from "react"
import { useEffect } from "react"
import { AuthGuard } from "@/components/auth-guard"
import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "@/components/ui/toaster"
import { CustomToastContainer } from "@/components/custom-toast"
import { updateLastActivity } from "@/lib/auth-utils"

export default function ClientLayout({ children }: { children: React.ReactNode }) {
  // 监听用户活动，更新最后活动时间
  useEffect(() => {
    const handleActivity = () => {
      updateLastActivity()
    }

    // 监听各种用户活动事件
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click']
    events.forEach(event => {
      document.addEventListener(event, handleActivity, true)
    })

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleActivity, true)
      })
    }
  }, [])

  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
      <AuthGuard>
        <div className="flex flex-col min-h-screen">
          {children}
          <Toaster />
          <CustomToastContainer />
        </div>
      </AuthGuard>
    </ThemeProvider>
  )
}
