import { NextRequest, NextResponse } from 'next/server'
import { ACCESS_PASSWORDS } from '@/lib/config'

export const runtime = 'edge'

/**
 * 用户认证成功后获取API访问密钥
 * 这个端点用于在用户通过页面密码认证后，获取API调用所需的密钥
 */
export async function POST(request: NextRequest) {
  try {
    const { password } = await request.json()

    // 验证密码
    if (!password || !ACCESS_PASSWORDS.includes(password.trim())) {
      return NextResponse.json(
        { 
          error: 'Invalid password',
          code: 'INVALID_PASSWORD'
        },
        { status: 401 }
      )
    }

    // 获取API访问密钥
    const apiKey = process.env.API_ACCESS_KEY
    if (!apiKey) {
      console.error('API_ACCESS_KEY environment variable is not configured')
      return NextResponse.json(
        { 
          error: 'API access key not configured',
          code: 'API_KEY_NOT_CONFIGURED'
        },
        { status: 500 }
      )
    }

    // 返回API密钥
    return NextResponse.json({
      success: true,
      apiKey: apiKey,
      message: 'Authentication successful'
    })

  } catch (error) {
    console.error('Auth API error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    )
  }
}
