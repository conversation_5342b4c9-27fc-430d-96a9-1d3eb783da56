import { NextRequest, NextResponse } from "next/server"
import { validateApiAccess } from "@/lib/api-auth"

export const runtime = "edge" // Use the edge runtime for faster responses

export async function POST(req: NextRequest) {
  // 验证API访问权限
  const authError = validateApiAccess(req)
  if (authError) {
    return authError
  }
  try {
    const { url } = await req.json()

    if (!url) {
      return NextResponse.json({ error: "URL is required" }, { status: 400 })
    }

    // Step 1: Fetch the content of the URL
    let pageContent = ""
    try {
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      pageContent = await response.text()
    } catch (fetchError) {
      console.error("Failed to fetch URL content:", fetchError)
      // If fetching fails, proceed with just the URL
      pageContent = `Could not fetch content from ${url}. Please analyze based on the URL itself.`
    }

    // Extract a relevant snippet if the content is too long
    const snippet = pageContent.slice(0, 5000) // Limit to first 5000 characters

    // Step 2: Use DeepSeek AI to analyze the content and extract information
    const systemPrompt = `你是一个专业的网站分析和工具分类专家。请根据用户提供的网站URL和页面内容，准确分析并生成以下信息：

1. 工具名称：提取网站的真实名称（如：腾讯视频、GitHub、Figma等）
2. 工具描述：生成50-100字的准确描述，说明工具的主要功能和特点
3. 相关标签：生成3-6个相关的中文标签
4. 分类信息：将工具分类到最合适的三级目录结构中

## 完整分类结构：

**development (开发工具)**
- code-editor (代码编辑): online-editor, ide, text-editor, code-formatter
- version-control (版本控制): git-tools, collaboration, code-review
- api-tools (API工具): api-testing, api-docs, mock-tools
- database (数据库工具): db-client, db-design, db-migration
- deployment (部署运维): ci-cd, monitoring, container

**design (设计工具)**
- ui-design (UI设计): prototyping, wireframe, design-system
- graphics (图形设计): vector-graphics, photo-editing, illustration
- color-tools (色彩工具): color-picker, color-palette, gradient
- icon-fonts (图标字体): icon-library, font-tools, emoji
- 3d-design (3D设计): 3d-modeling, 3d-rendering, 3d-animation

**productivity (效率工具)**
- note-taking (笔记工具): markdown, knowledge-base, mind-map
- task-management (任务管理): todo-list, project-management, time-tracking
- automation (自动化工具): workflow, scripting, integration
- office-tools (办公工具): document, spreadsheet, presentation

**learning (学习资源)**
- programming (编程学习): coding-practice, algorithm, tutorial
- language (语言学习): vocabulary, grammar, pronunciation
- reference (参考资料): documentation, cheatsheet, examples
- online-courses (在线课程): mooc, video-courses, certification

**entertainment (娱乐工具)**
- media-streaming (媒体播放): video-streaming, music-streaming, podcast
- games (游戏娱乐): online-games, game-tools, game-community
- content-download (内容下载): video-download, music-download, movie-download
- social-entertainment (社交娱乐): social-media, chat-tools, community

**life-service (生活服务)**
- daily-tools (日常工具): weather, calendar, reminder
- travel (旅行出行): map-navigation, booking, travel-guide
- health-fitness (健康健身): fitness-tracker, health-monitor, nutrition
- finance (金融理财): budget-tracker, investment, currency

**business (商业工具)**
- marketing (营销推广): seo-tools, social-marketing, email-marketing
- analytics (数据分析): web-analytics, business-intelligence, data-visualization
- customer-service (客户服务): live-chat, help-desk, feedback
- e-commerce (电子商务): online-store, payment, inventory

**system (系统工具)**
- network-tools (网络工具): speed-test, dns-tools, proxy-vpn
- security (安全工具): password-manager, encryption, security-scan
- file-tools (文件工具): file-converter, file-compress, file-recovery
- system-monitor (系统监控): performance, resource-usage, system-info

**ai-tools (AI工具)**
- text-generation (文本生成): chatbot, writing-assistant, content-creation
- image-generation (图像生成): ai-art, photo-enhancement, image-editing
- code-assistant (代码助手): code-completion, code-review, bug-detection
- data-analysis (数据分析): data-mining, predictive-analysis, pattern-recognition

**other (其他工具)**
- utility (实用工具): calculator, unit-converter, qr-generator
- generator (生成工具): text-generator, image-generator, data-generator
- testing (测试工具): website-test, performance-test, compatibility-test

## 分析要求：
1. 仔细分析URL和页面内容，识别网站的真实身份
2. 生成准确的中文名称和描述
3. 选择最合适的三级分类
4. 如果现有分类不合适，可以建议新的二级或三级分类
5. 必须返回标准的JSON格式

请返回JSON格式的结果，包含以下字段：
{
  "name": "工具的真实名称",
  "description": "50-100字的准确描述",
  "tags": ["标签1", "标签2", "标签3"],
  "category": "一级分类ID",
  "subcategory": "二级分类ID",
  "subsubcategory": "三级分类ID"
}`

    const userPrompt = `请分析以下网站：
URL: ${url}
页面内容片段: ${snippet.substring(0, 2000)}`

    // Call DeepSeek API
    const response = await fetch(process.env.DEEPSEEK_API_URL || 'https://api.deepseek.com/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.DEEPSEEK_API_KEY}`
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt }
        ],
        temperature: 0.3,
        max_tokens: 1000,
        stream: false
      })
    })

    if (!response.ok) {
      throw new Error(`DeepSeek API error: ${response.status}`)
    }

    const aiResponse = await response.json()
    const text = aiResponse.choices[0]?.message?.content || ''

    // Step 3: Parse the AI's response
    let parsedResponse: any
    try {
      // Try to extract JSON from the response if it's wrapped in markdown or other text
      let jsonText = text.trim()

      // Remove markdown code blocks if present
      if (jsonText.includes('```json')) {
        const jsonMatch = jsonText.match(/```json\s*([\s\S]*?)\s*```/)
        if (jsonMatch) {
          jsonText = jsonMatch[1].trim()
        }
      } else if (jsonText.includes('```')) {
        const jsonMatch = jsonText.match(/```\s*([\s\S]*?)\s*```/)
        if (jsonMatch) {
          jsonText = jsonMatch[1].trim()
        }
      }

      // Find JSON object in the text
      const jsonMatch = jsonText.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        jsonText = jsonMatch[0]
      }

      parsedResponse = JSON.parse(jsonText)
    } catch (parseError) {
      console.error("Failed to parse AI response as JSON:", parseError)
      console.error("AI raw response:", text)

      // Try to extract information manually as fallback
      const fallbackName = extractFromText(text, ['name', '名称', 'title']) || extractNameFromUrl(url)
      const fallbackDescription = extractFromText(text, ['description', '描述', 'desc']) || `${fallbackName}是一个实用的在线工具`

      return NextResponse.json({
        name: fallbackName,
        description: fallbackDescription,
        tags: extractTagsFromText(text, url),
        category: "other",
        subcategory: "utility",
        subsubcategory: "calculator",
        error: "AI response parsing failed, using fallback analysis"
      })
    }

    // Validate and sanitize the parsed response
    const validatedResponse = {
      name: parsedResponse.name || extractNameFromUrl(url),
      description: parsedResponse.description || `${parsedResponse.name || '工具'}的在线服务平台`,
      tags: Array.isArray(parsedResponse.tags) ? parsedResponse.tags.slice(0, 6) : extractTagsFromUrl(url),
      category: parsedResponse.category || "other",
      subcategory: parsedResponse.subcategory || "utility",
      subsubcategory: parsedResponse.subsubcategory || "calculator",
    }

    return NextResponse.json(validatedResponse)
  } catch (error) {
    console.error("Error in analyze-url API:", error)
    return NextResponse.json(
      {
        error: "Internal server error during URL analysis",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    )
  }
}

// Helper functions for fallback analysis
function extractFromText(text: string, keywords: string[]): string {
  for (const keyword of keywords) {
    const regex = new RegExp(`${keyword}[："：]?\\s*["']?([^"'\\n,，。]{1,50})`, 'i')
    const match = text.match(regex)
    if (match && match[1]) {
      return match[1].trim()
    }
  }
  return ''
}

function extractNameFromUrl(url: string): string {
  try {
    const domain = new URL(url).hostname.replace('www.', '')
    const parts = domain.split('.')
    if (parts.length >= 2) {
      const name = parts[0]
      // Common domain to name mappings
      const nameMap: { [key: string]: string } = {
        'qq': '腾讯',
        'baidu': '百度',
        'taobao': '淘宝',
        'tmall': '天猫',
        'jd': '京东',
        'weibo': '微博',
        'zhihu': '知乎',
        'bilibili': '哔哩哔哩',
        'douyin': '抖音',
        'tiktok': 'TikTok',
        'youtube': 'YouTube',
        'github': 'GitHub',
        'google': 'Google',
        'microsoft': 'Microsoft',
        'apple': 'Apple',
        'amazon': 'Amazon',
        'netflix': 'Netflix'
      }
      return nameMap[name] || name.charAt(0).toUpperCase() + name.slice(1)
    }
    return domain
  } catch {
    return '未知工具'
  }
}

function extractTagsFromText(text: string, url: string): string[] {
  const tags: string[] = []
  try {
    const domain = new URL(url).hostname.toLowerCase()

    // Add domain-based tags
    if (domain.includes('video') || domain.includes('tv')) tags.push('视频')
    if (domain.includes('music')) tags.push('音乐')
    if (domain.includes('game')) tags.push('游戏')
    if (domain.includes('shop') || domain.includes('store')) tags.push('购物')
    if (domain.includes('news')) tags.push('新闻')
    if (domain.includes('edu')) tags.push('教育')
  } catch {
    // Ignore URL parsing errors
  }

  return tags.slice(0, 3)
}

function extractTagsFromUrl(url: string): string[] {
  return extractTagsFromText('', url)
}
