import { NextRequest, NextResponse } from "next/server"
import { AIService } from "@/lib/ai-service"
import { validateApiAccess } from "@/lib/api-auth"

export const runtime = "edge"

export async function POST(req: NextRequest) {
  // 验证API访问权限
  const authError = validateApiAccess(req)
  if (authError) {
    return authError
  }
  try {
    const { query, tools } = await req.json()

    if (!query || !tools) {
      return NextResponse.json(
        { error: "Query and tools are required" },
        { status: 400 }
      )
    }

    if (!Array.isArray(tools)) {
      return NextResponse.json(
        { error: "Tools must be an array" },
        { status: 400 }
      )
    }

    // 调用AI深度搜索服务
    const result = await AIService.deepSearch(query, tools)

    // 验证返回结果的结构
    if (!result || typeof result !== 'object') {
      throw new Error('AI返回的结果格式无效')
    }

    // 处理两种可能的返回格式
    let finalResult
    if (Array.isArray(result)) {
      // 如果AI直接返回了推荐工具数组，包装成完整格式
      finalResult = {
        searchSummary: `为您找到 ${result.length} 个相关的社区论坛工具`,
        recommendedTools: result,
        searchInsights: `基于您的查询，推荐了 ${result.length} 个最相关的工具，按相关性排序`
      }
    } else {
      // 如果AI返回了完整的对象格式
      finalResult = result
    }

    // 确保recommendedTools是数组
    if (!finalResult.recommendedTools || !Array.isArray(finalResult.recommendedTools)) {
      finalResult.recommendedTools = []
    }

    // 验证每个推荐工具的结构
    finalResult.recommendedTools = finalResult.recommendedTools.filter(rec =>
      rec && typeof rec === 'object' && rec.id && typeof rec.relevanceScore === 'number'
    )

    return NextResponse.json({
      success: true,
      data: finalResult
    })

  } catch (error) {
    console.error("Deep search API error:", error)

    return NextResponse.json(
      {
        error: "Deep search failed",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    )
  }
}
