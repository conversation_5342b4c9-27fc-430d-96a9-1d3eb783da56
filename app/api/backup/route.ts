import { NextRequest, NextResponse } from 'next/server'
import { BackupService } from '@/lib/backup-service'

/**
 * 自动备份API端点
 * GET /api/backup - 执行自动备份
 * GET /api/backup?action=test - 测试COS连接
 * GET /api/backup?action=history - 获取备份历史
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    // 验证API访问权限（可选，增加安全性）
    const apiKey = request.headers.get('x-api-key')
    const expectedApiKey = process.env.API_ACCESS_KEY
    
    if (expectedApiKey && apiKey !== expectedApiKey) {
      return NextResponse.json(
        { 
          success: false, 
          message: '无效的API密钥',
          timestamp: new Date().toISOString()
        },
        { status: 401 }
      )
    }

    switch (action) {
      case 'test':
        // 测试COS连接
        const testResult = await BackupService.testCOSConnection()
        return NextResponse.json({
          ...testResult,
          timestamp: new Date().toISOString()
        })

      case 'history':
        // 获取备份历史
        const limit = parseInt(searchParams.get('limit') || '10')
        const historyResult = await BackupService.getBackupHistory(limit)
        return NextResponse.json({
          ...historyResult,
          timestamp: new Date().toISOString()
        })

      default:
        // 执行自动备份
        console.log('收到自动备份请求')
        const backupResult = await BackupService.performAutoBackup()
        
        return NextResponse.json({
          ...backupResult,
          timestamp: new Date().toISOString()
        }, {
          status: backupResult.success ? 200 : 500
        })
    }

  } catch (error) {
    console.error('备份API错误:', error)
    
    return NextResponse.json(
      {
        success: false,
        message: `备份API执行失败: ${error instanceof Error ? error.message : '未知错误'}`,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

/**
 * POST方法支持手动触发备份
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json().catch(() => ({}))
    const { action } = body

    // 验证API访问权限
    const apiKey = request.headers.get('x-api-key')
    const expectedApiKey = process.env.API_ACCESS_KEY
    
    if (expectedApiKey && apiKey !== expectedApiKey) {
      return NextResponse.json(
        { 
          success: false, 
          message: '无效的API密钥',
          timestamp: new Date().toISOString()
        },
        { status: 401 }
      )
    }

    switch (action) {
      case 'manual':
        // 手动触发备份
        console.log('收到手动备份请求')
        const backupResult = await BackupService.performAutoBackup()
        
        return NextResponse.json({
          ...backupResult,
          timestamp: new Date().toISOString()
        }, {
          status: backupResult.success ? 200 : 500
        })

      default:
        return NextResponse.json(
          {
            success: false,
            message: '不支持的操作类型',
            timestamp: new Date().toISOString()
          },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('备份API错误:', error)
    
    return NextResponse.json(
      {
        success: false,
        message: `备份API执行失败: ${error instanceof Error ? error.message : '未知错误'}`,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}
