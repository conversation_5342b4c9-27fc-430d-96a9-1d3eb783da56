import { NextRequest, NextResponse } from "next/server"
import { AIService } from "@/lib/ai-service"
import { validateApiAccess } from "@/lib/api-auth"

export const runtime = "edge"

export async function POST(req: NextRequest) {
  // 验证API访问权限
  const authError = validateApiAccess(req)
  if (authError) {
    return authError
  }
  try {
    const { query } = await req.json()

    if (!query) {
      return NextResponse.json(
        { error: "Query is required" },
        { status: 400 }
      )
    }

    if (typeof query !== 'string' || query.trim().length === 0) {
      return NextResponse.json(
        { error: "Query must be a non-empty string" },
        { status: 400 }
      )
    }

    // 调用AI全网搜索服务
    const result = await AIService.globalSearch(query.trim())

    // 验证返回结果的结构
    if (!result || typeof result !== 'object') {
      throw new Error('AI返回的结果格式无效')
    }

    // 处理两种可能的返回格式
    let finalResult
    if (Array.isArray(result)) {
      // 如果AI直接返回了推荐工具数组，包装成完整格式
      finalResult = {
        searchSummary: `为您的全网搜索找到了 ${result.length} 个优秀工具`,
        recommendedTools: result,
        searchInsights: `基于全网搜索，我们为您推荐了 ${result.length} 个最优质的工具，按相关性和质量排序`
      }
    } else {
      // 如果AI返回了完整的对象格式
      finalResult = result
    }

    // 确保recommendedTools是数组
    if (!finalResult.recommendedTools || !Array.isArray(finalResult.recommendedTools)) {
      finalResult.recommendedTools = []
    }

    // 验证每个推荐工具的结构
    finalResult.recommendedTools = finalResult.recommendedTools.filter(tool =>
      tool &&
      typeof tool === 'object' &&
      tool.name &&
      tool.url &&
      typeof tool.relevanceScore === 'number'
    )

    return NextResponse.json({
      success: true,
      data: finalResult
    })

  } catch (error) {
    console.error("Global search API error:", error)
    
    return NextResponse.json(
      {
        error: "Global search failed",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    )
  }
}
