"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { clearAuthStatus } from "@/lib/auth-utils"
import { useRouter } from "next/navigation"

export default function TestPage() {
  const router = useRouter()

  const handleLogout = () => {
    clearAuthStatus()
    router.push("/")
  }

  return (
    <div className="container mx-auto p-4">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>测试页面</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p>如果您能看到这个页面，说明认证系统正常工作。</p>
          <Button onClick={handleLogout} variant="destructive" className="w-full">
            退出登录（测试会话清除）
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}
