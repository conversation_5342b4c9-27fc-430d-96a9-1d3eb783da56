# ToolMaster - 工具大师

ToolMaster 是一个高效、智能的在线工具箱，旨在帮助用户管理和发现各类实用工具。

## 功能特性

- **工具管理**: 添加、编辑、删除和分类你的工具。
- **智能分类**: 通过 AI 自动分析工具信息并进行分类和打标签。
- **快速搜索**: 强大的搜索功能，快速找到所需工具。
- **数据导入导出**: 方便地备份和迁移你的工具数据。
- **响应式设计**: 在不同设备上提供一致的用户体验。

## 技术栈

- **Next.js**: React 框架，用于构建用户界面。
- **Tailwind CSS**: 实用至上的 CSS 框架，用于快速构建美观的界面。
- **shadcn/ui**: 基于 Tailwind CSS 和 Radix UI 的可复用组件库。
- **Lucide React**: 简洁的开源图标库。
- **AI SDK**: Vercel 提供的 AI SDK，用于集成 AI 模型（如 OpenAI GPT-4o）。
- **Local Storage**: 浏览器本地存储，用于持久化工具和分类数据。

## 快速开始

### 1. 克隆仓库

\`\`\`bash
git clone https://github.com/your-username/toolmaster.git
cd toolmaster
\`\`\`

### 2. 安装依赖

\`\`\`bash
npm install
# 或
yarn install
# 或
pnpm install
\`\`\`

### 3. 配置环境变量

创建一个 `.env.local` 文件在项目根目录，并添加你的 OpenAI API 密钥：

\`\`\`
OPENAI_API_KEY=YOUR_OPENAI_API_KEY
\`\`\`

### 4. 运行开发服务器

\`\`\`bash
npm run dev
# 或
yarn dev
# 或
pnpm dev
\`\`\`

在浏览器中打开 [http://localhost:3000](http://localhost:3000) 即可查看应用程序。

## 部署

你可以轻松地将 ToolMaster 部署到 Vercel。

### 1. 准备 Vercel 项目

- 确保你已经安装了 Vercel CLI (`npm i -g vercel`).
- 登录 Vercel (`vercel login`).
- 在你的项目目录中运行 `vercel` 命令，并按照提示进行操作。

### 2. 配置环境变量

在 Vercel 项目设置中添加 `OPENAI_API_KEY` 环境变量。

### 3. 部署

\`\`\`bash
vercel deploy
\`\`\`

## 贡献

欢迎贡献！如果你有任何建议或发现 Bug，请随时提交 Issue 或 Pull Request。

## 许可证

[MIT License](LICENSE)
