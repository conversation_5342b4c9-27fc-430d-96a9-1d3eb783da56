"use client"

import { useState, useEffect } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Cloud, CloudOff, RefreshCw, Wifi, WifiOff } from 'lucide-react'
import { hybridStorage } from '@/lib/hybrid-storage'

export function SyncStatus() {
  const [syncStatus, setSyncStatus] = useState({
    isOnline: true,
    syncing: false
  })

  useEffect(() => {
    // 初始化状态
    setSyncStatus(hybridStorage.getSyncStatus())

    // 监听网络状态变化
    const handleOnline = () => setSyncStatus(prev => ({ ...prev, isOnline: true }))
    const handleOffline = () => setSyncStatus(prev => ({ ...prev, isOnline: false }))

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  const handleManualSync = async () => {
    setSyncStatus(prev => ({ ...prev, syncing: true }))
    try {
      await hybridStorage.manualSync()
    } finally {
      setSyncStatus(prev => ({ ...prev, syncing: false }))
    }
  }

  const getStatusInfo = () => {
    if (!syncStatus.isOnline) {
      return {
        icon: <WifiOff className="h-3 w-3" />,
        text: '离线模式',
        variant: 'secondary' as const,
        tooltip: '当前处于离线状态，数据仅保存在本地'
      }
    }

    if (syncStatus.syncing) {
      return {
        icon: <RefreshCw className="h-3 w-3 animate-spin" />,
        text: '同步中',
        variant: 'default' as const,
        tooltip: '正在与云端同步数据...'
      }
    }

    return {
      icon: <Cloud className="h-3 w-3" />,
      text: '已同步',
      variant: 'default' as const,
      tooltip: '数据已与云端同步'
    }
  }

  const statusInfo = getStatusInfo()

  return (
    <div className="flex items-center gap-2">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge variant={statusInfo.variant} className="gap-1 text-xs">
              {statusInfo.icon}
              <span className="hidden sm:inline">{statusInfo.text}</span>
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <p>{statusInfo.tooltip}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      {syncStatus.isOnline && !syncStatus.syncing && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <button
                onClick={handleManualSync}
                className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-6 w-6 p-0"
              >
                <RefreshCw className="h-3 w-3" />
              </button>
            </TooltipTrigger>
            <TooltipContent>
              <p>手动同步</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  )
}
