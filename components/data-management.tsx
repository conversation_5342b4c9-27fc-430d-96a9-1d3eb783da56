"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON><PERSON>nt, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { useCustomToast } from "@/hooks/use-custom-toast"
import type { Tool, Category } from "@/components/toolbox"

interface DataManagementProps {
  isOpen: boolean
  onClose: () => void
  onImportData: (data: { tools: Tool[]; categories: Category[] }) => void
  onExportData: () => { tools: Tool[]; categories: Category[] }
}

export function DataManagement({ isOpen, onClose, onImportData, onExportData }: DataManagementProps) {
  const [importDataString, setImportDataString] = useState("")
  const [exportDataString, setExportDataString] = useState("")
  const { toast } = useToast()
  const customToast = useCustomToast()

  const handleExport = () => {
    try {
      const data = onExportData()
      setExportDataString(JSON.stringify(data, null, 2))
      customToast.success("导出成功", "数据已生成，请复制。")
    } catch (error) {
      console.error("Export data error:", error)
      customToast.error("导出失败", "生成导出数据时发生错误。")
    }
  }

  const handleImport = () => {
    try {
      const data = JSON.parse(importDataString)
      if (data && Array.isArray(data.tools) && Array.isArray(data.categories)) {
        onImportData(data)
        customToast.success("导入成功", "工具和分类数据已成功导入。")
        onClose() // Close dialog on successful import
      } else {
        throw new Error("Invalid data format")
      }
    } catch (error) {
      console.error("Import data error:", error)
      customToast.error("导入失败", "导入数据格式不正确或解析失败。")
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>数据管理</DialogTitle>
          <DialogDescription>导入或导出你的工具箱数据。</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          {/* Export Section */}
          <div>
            <h3 className="text-lg font-semibold mb-2">导出数据</h3>
            <Button onClick={handleExport} className="mb-2">
              生成导出数据
            </Button>
            {exportDataString && (
              <Textarea
                value={exportDataString}
                readOnly
                rows={10}
                className="font-mono text-xs"
                onClick={(e) => (e.target as HTMLTextAreaElement).select()}
              />
            )}
            <p className="text-sm text-muted-foreground mt-2">
              点击上方按钮生成 JSON 数据，然后复制粘贴到安全的地方进行备份。
            </p>
          </div>

          {/* Separator */}
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">或</span>
          </div>

          {/* Import Section */}
          <div>
            <h3 className="text-lg font-semibold mb-2">导入数据</h3>
            <Textarea
              placeholder="粘贴你的 JSON 数据到这里..."
              value={importDataString}
              onChange={(e) => setImportDataString(e.target.value)}
              rows={10}
              className="font-mono text-xs mb-2"
            />
            <Button onClick={handleImport} disabled={!importDataString.trim()}>
              导入数据
            </Button>
            <p className="text-sm text-muted-foreground mt-2">
              粘贴之前导出的 JSON 数据到文本框，然后点击导入。这将覆盖现有数据。
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
