"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON><PERSON>nt, <PERSON>alog<PERSON>eader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Plus, Trash2, Edit } from "lucide-react"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import type { Category } from "@/components/toolbox"

interface CategoryManagerProps {
  isOpen: boolean
  onClose: () => void
  categories: Category[]
  onUpdateCategories: (categories: Category[]) => void
}

export function CategoryManager({ isOpen, onClose, categories, onUpdateCategories }: CategoryManagerProps) {
  const [newCategoryName, setNewCategoryName] = useState("")
  const [newSubcategoryName, setNewSubcategoryName] = useState("")
  const [newSubsubcategoryName, setNewSubsubcategoryName] = useState("")

  const [selectedCategoryId, setSelectedCategoryId] = useState<string | null>(null)
  const [selectedSubcategoryId, setSelectedSubcategoryId] = useState<string | null>(null)

  const [editCategoryId, setEditCategoryId] = useState<string | null>(null)
  const [editCategoryName, setEditCategoryName] = useState("")
  const [editSubcategoryId, setEditSubcategoryId] = useState<string | null>(null)
  const [editSubcategoryName, setEditSubcategoryName] = useState("")
  const [editSubsubcategoryId, setEditSubsubcategoryId] = useState<string | null>(null)
  const [editSubsubcategoryName, setEditSubsubcategoryName] = useState("")

  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [deleteTarget, setDeleteTarget] = useState<{
    type: "category" | "subcategory" | "subsubcategory"
    id: string
    parentId?: string
    grandparentId?: string
    name: string
  } | null>(null)

  const currentCategory = categories.find((c) => c.id === selectedCategoryId)
  const currentSubcategory = currentCategory?.subcategories.find((s) => s.id === selectedSubcategoryId)

  const handleAddCategory = () => {
    if (newCategoryName.trim()) {
      const newCategory: Category = {
        id: Date.now().toString(),
        name: newCategoryName.trim(),
        subcategories: [],
      }
      onUpdateCategories([...categories, newCategory])
      setNewCategoryName("")
    }
  }

  const handleAddSubcategory = () => {
    if (selectedCategoryId && newSubcategoryName.trim()) {
      const updatedCategories = categories.map((cat) => {
        if (cat.id === selectedCategoryId) {
          return {
            ...cat,
            subcategories: [
              ...cat.subcategories,
              { id: Date.now().toString(), name: newSubcategoryName.trim(), subsubcategories: [] },
            ],
          }
        }
        return cat
      })
      onUpdateCategories(updatedCategories)
      setNewSubcategoryName("")
    }
  }

  const handleAddSubsubcategory = () => {
    if (selectedCategoryId && selectedSubcategoryId && newSubsubcategoryName.trim()) {
      const updatedCategories = categories.map((cat) => {
        if (cat.id === selectedCategoryId) {
          return {
            ...cat,
            subcategories: cat.subcategories.map((subcat) => {
              if (subcat.id === selectedSubcategoryId) {
                return {
                  ...subcat,
                  subsubcategories: [
                    ...subcat.subsubcategories,
                    { id: Date.now().toString(), name: newSubsubcategoryName.trim() },
                  ],
                }
              }
              return subcat
            }),
          }
        }
        return cat
      })
      onUpdateCategories(updatedCategories)
      setNewSubsubcategoryName("")
    }
  }

  const handleDeleteClick = (
    type: "category" | "subcategory" | "subsubcategory",
    id: string,
    name: string,
    parentId?: string,
    grandparentId?: string,
  ) => {
    setDeleteTarget({ type, id, name, parentId, grandparentId })
    setShowDeleteConfirm(true)
  }

  const confirmDelete = () => {
    if (!deleteTarget) return

    let updatedCategories = [...categories]

    if (deleteTarget.type === "category") {
      updatedCategories = updatedCategories.filter((cat) => cat.id !== deleteTarget.id)
      setSelectedCategoryId(null)
      setSelectedSubcategoryId(null)
    } else if (deleteTarget.type === "subcategory" && deleteTarget.parentId) {
      updatedCategories = updatedCategories.map((cat) => {
        if (cat.id === deleteTarget.parentId) {
          return {
            ...cat,
            subcategories: cat.subcategories.filter((subcat) => subcat.id !== deleteTarget.id),
          }
        }
        return cat
      })
      setSelectedSubcategoryId(null)
    } else if (deleteTarget.type === "subsubcategory" && deleteTarget.parentId && deleteTarget.grandparentId) {
      updatedCategories = updatedCategories.map((cat) => {
        if (cat.id === deleteTarget.grandparentId) {
          return {
            ...cat,
            subcategories: cat.subcategories.map((subcat) => {
              if (subcat.id === deleteTarget.parentId) {
                return {
                  ...subcat,
                  subsubcategories: subcat.subsubcategories.filter((subsub) => subsub.id !== deleteTarget.id),
                }
              }
              return subcat
            }),
          }
        }
        return cat
      })
    }
    onUpdateCategories(updatedCategories)
    setShowDeleteConfirm(false)
    setDeleteTarget(null)
  }

  const startEditCategory = (category: Category) => {
    setEditCategoryId(category.id)
    setEditCategoryName(category.name)
  }

  const saveEditCategory = () => {
    if (editCategoryId && editCategoryName.trim()) {
      const updatedCategories = categories.map((cat) =>
        cat.id === editCategoryId ? { ...cat, name: editCategoryName.trim() } : cat,
      )
      onUpdateCategories(updatedCategories)
      setEditCategoryId(null)
      setEditCategoryName("")
    }
  }

  const startEditSubcategory = (subcat: Category["subcategories"][number], parentId: string) => {
    setEditSubcategoryId(subcat.id)
    setEditSubcategoryName(subcat.name)
    setSelectedCategoryId(parentId) // Ensure parent is selected for context
  }

  const saveEditSubcategory = () => {
    if (editSubcategoryId && editSubcategoryName.trim() && selectedCategoryId) {
      const updatedCategories = categories.map((cat) => {
        if (cat.id === selectedCategoryId) {
          return {
            ...cat,
            subcategories: cat.subcategories.map((subcat) =>
              subcat.id === editSubcategoryId ? { ...subcat, name: editSubcategoryName.trim() } : subcat,
            ),
          }
        }
        return cat
      })
      onUpdateCategories(updatedCategories)
      setEditSubcategoryId(null)
      setEditSubcategoryName("")
    }
  }

  const startEditSubsubcategory = (
    subsub: Category["subcategories"][number]["subsubcategories"][number],
    parentId: string,
    grandparentId: string,
  ) => {
    setEditSubsubcategoryId(subsub.id)
    setEditSubsubcategoryName(subsub.name)
    setSelectedCategoryId(grandparentId) // Ensure grand-parent is selected for context
    setSelectedSubcategoryId(parentId) // Ensure parent is selected for context
  }

  const saveEditSubsubcategory = () => {
    if (editSubsubcategoryId && editSubsubcategoryName.trim() && selectedCategoryId && selectedSubcategoryId) {
      const updatedCategories = categories.map((cat) => {
        if (cat.id === selectedCategoryId) {
          return {
            ...cat,
            subcategories: cat.subcategories.map((subcat) => {
              if (subcat.id === selectedSubcategoryId) {
                return {
                  ...subcat,
                  subsubcategories: subcat.subsubcategories.map((subsub) =>
                    subsub.id === editSubsubcategoryId ? { ...subsub, name: editSubsubcategoryName.trim() } : subsub,
                  ),
                }
              }
              return subcat
            }),
          }
        }
        return cat
      })
      onUpdateCategories(updatedCategories)
      setEditSubsubcategoryId(null)
      setEditSubsubcategoryName("")
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>分类管理</DialogTitle>
          <DialogDescription>添加、编辑或删除你的工具分类。</DialogDescription>
        </DialogHeader>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 flex-1 overflow-hidden">
          {/* Category Column */}
          <div className="flex flex-col border rounded-md p-4">
            <h4 className="font-semibold mb-3 text-base">一级分类</h4>
            <div className="flex gap-2 mb-4">
              <Input
                placeholder="新一级分类名称"
                value={newCategoryName}
                onChange={(e) => setNewCategoryName(e.target.value)}
                onKeyPress={(e) => e.key === "Enter" && handleAddCategory()}
                className="text-sm"
              />
              <Button size="icon" onClick={handleAddCategory}>
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <ScrollArea className="flex-1 pr-2">
              <div className="space-y-2">
                {categories.map((cat) => (
                  <div
                    key={cat.id}
                    className={`flex items-center justify-between p-2 rounded-md cursor-pointer ${
                      selectedCategoryId === cat.id ? "bg-muted" : "hover:bg-muted/50"
                    }`}
                    onClick={() => {
                      setSelectedCategoryId(cat.id)
                      setSelectedSubcategoryId(null)
                    }}
                  >
                    {editCategoryId === cat.id ? (
                      <Input
                        value={editCategoryName}
                        onChange={(e) => setEditCategoryName(e.target.value)}
                        onBlur={saveEditCategory}
                        onKeyPress={(e) => e.key === "Enter" && saveEditCategory()}
                        className="h-8 mr-2"
                      />
                    ) : (
                      <span className="flex-1">{cat.name}</span>
                    )}
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="w-7 h-7"
                        onClick={(e) => {
                          e.stopPropagation()
                          startEditCategory(cat)
                        }}
                      >
                        <Edit className="h-3.5 w-3.5" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="w-7 h-7 text-red-500 hover:text-red-600"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDeleteClick("category", cat.id, cat.name)
                        }}
                      >
                        <Trash2 className="h-3.5 w-3.5" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>

          {/* Subcategory Column */}
          <div className="flex flex-col border rounded-md p-4">
            <h4 className="font-semibold mb-3 text-base">二级分类</h4>
            {selectedCategoryId ? (
              <>
                <div className="flex gap-2 mb-4">
                  <Input
                    placeholder="新二级分类名称"
                    value={newSubcategoryName}
                    onChange={(e) => setNewSubcategoryName(e.target.value)}
                    onKeyPress={(e) => e.key === "Enter" && handleAddSubcategory()}
                    className="text-sm"
                  />
                  <Button size="icon" onClick={handleAddSubcategory}>
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <ScrollArea className="flex-1 pr-2">
                  <div className="space-y-2">
                    {currentCategory?.subcategories.map((subcat) => (
                      <div
                        key={subcat.id}
                        className={`flex items-center justify-between p-2 rounded-md cursor-pointer ${
                          selectedSubcategoryId === subcat.id ? "bg-muted" : "hover:bg-muted/50"
                        }`}
                        onClick={() => setSelectedSubcategoryId(subcat.id)}
                      >
                        {editSubcategoryId === subcat.id ? (
                          <Input
                            value={editSubcategoryName}
                            onChange={(e) => setEditSubcategoryName(e.target.value)}
                            onBlur={saveEditSubcategory}
                            onKeyPress={(e) => e.key === "Enter" && saveEditSubcategory()}
                            className="h-8 mr-2"
                          />
                        ) : (
                          <span className="flex-1">{subcat.name}</span>
                        )}
                        <div className="flex gap-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="w-7 h-7"
                            onClick={(e) => {
                              e.stopPropagation()
                              startEditSubcategory(subcat, selectedCategoryId)
                            }}
                          >
                            <Edit className="h-3.5 w-3.5" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="w-7 h-7 text-red-500 hover:text-red-600"
                            onClick={(e) => {
                              e.stopPropagation()
                              handleDeleteClick("subcategory", subcat.id, subcat.name, selectedCategoryId)
                            }}
                          >
                            <Trash2 className="h-3.5 w-3.5" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </>
            ) : (
              <p className="text-muted-foreground text-sm">请先选择一个主分类。</p>
            )}
          </div>

          {/* Sub-subcategory Column */}
          <div className="flex flex-col border rounded-md p-4">
            <h4 className="font-semibold mb-3 text-base">三级分类</h4>
            {selectedSubcategoryId ? (
              <>
                <div className="flex gap-2 mb-4">
                  <Input
                    placeholder="新三级分类名称"
                    value={newSubsubcategoryName}
                    onChange={(e) => setNewSubsubcategoryName(e.target.value)}
                    onKeyPress={(e) => e.key === "Enter" && handleAddSubsubcategory()}
                    className="text-sm"
                  />
                  <Button size="icon" onClick={handleAddSubsubcategory}>
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <ScrollArea className="flex-1 pr-2">
                  <div className="space-y-2">
                    {currentSubcategory?.subsubcategories.map((subsub) => (
                      <div
                        key={subsub.id}
                        className="flex items-center justify-between p-2 rounded-md hover:bg-muted/50"
                      >
                        {editSubsubcategoryId === subsub.id ? (
                          <Input
                            value={editSubsubcategoryName}
                            onChange={(e) => setEditSubsubcategoryName(e.target.value)}
                            onBlur={saveEditSubsubcategory}
                            onKeyPress={(e) => e.key === "Enter" && saveEditSubsubcategory()}
                            className="h-8 mr-2"
                          />
                        ) : (
                          <span className="flex-1">{subsub.name}</span>
                        )}
                        <div className="flex gap-1">
                          <Button
                            variant="ghost"
                            size="icon"
                            className="w-7 h-7"
                            onClick={(e) => {
                              e.stopPropagation()
                              if (selectedCategoryId && selectedSubcategoryId) {
                                startEditSubsubcategory(subsub, selectedSubcategoryId, selectedCategoryId)
                              }
                            }}
                          >
                            <Edit className="h-3.5 w-3.5" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="w-7 h-7 text-red-500 hover:text-red-600"
                            onClick={(e) => {
                              e.stopPropagation()
                              if (selectedCategoryId && selectedSubcategoryId) {
                                handleDeleteClick(
                                  "subsubcategory",
                                  subsub.id,
                                  subsub.name,
                                  selectedSubcategoryId,
                                  selectedCategoryId,
                                )
                              }
                            }}
                          >
                            <Trash2 className="h-3.5 w-3.5" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </>
            ) : (
              <p className="text-muted-foreground text-sm">请先选择一个二级分类。</p>
            )}
          </div>
        </div>
        <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>确认删除？</AlertDialogTitle>
              <AlertDialogDescription>
                您确定要删除 "{deleteTarget?.name}" 吗？此操作将同时删除其下的所有下级分类和工具，且不可撤销。
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>取消</AlertDialogCancel>
              <AlertDialogAction onClick={confirmDelete} className="bg-red-600 hover:bg-red-700">
                确认删除
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </DialogContent>
    </Dialog>
  )
}
