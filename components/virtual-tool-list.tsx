import { ToolCard } from "@/components/tool-card"
import type { Tool } from "@/components/toolbox"

interface VirtualToolListProps {
  tools: Tool[]
  getCategoryPath: (categoryId: string, subcategoryId: string, subsubcategoryId: string) => string
  onCategoryPathClick: (category: string, subcategory: string, subsubCategory: string) => void
  onTagClick: (tag: string) => void
  onDeleteTool: (id: string) => void
  onCopyToolInfo: (type: "name" | "url" | "description", value: string) => void
  onViewDescription: (tool: Tool) => void
}

// This component is currently not used in app/page.tsx but is kept for future virtualization needs.
// It would typically be integrated with a library like react-window or react-virtualized.
export function VirtualToolList({
  tools,
  getCategoryPath,
  onCategoryPathClick,
  onTagClick,
  onDeleteTool,
  onCopyToolInfo,
  onViewDescription,
}: VirtualToolListProps) {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {tools.map((tool) => (
        <ToolCard
          key={tool.id}
          tool={tool}
          getCategoryPath={getCategoryPath}
          onCategoryPathClick={onCategoryPathClick}
          onTagClick={onTagClick}
          onDeleteTool={onDeleteTool}
          onCopyToolInfo={onCopyToolInfo}
          onViewDescription={onViewDescription}
        />
      ))}
    </div>
  )
}
