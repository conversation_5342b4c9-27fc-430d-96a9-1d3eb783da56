"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>onte<PERSON>, <PERSON>alog<PERSON>eader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import type { Category } from "@/components/toolbox"

interface AdvancedSearchProps {
  isOpen: boolean
  onClose: () => void
  onSearch: (filters: {
    query: string
    category: string
    subcategory: string
    subsubcategory: string
    tags: string
    sensitive: boolean
  }) => void
  categories: Category[]
  initialFilters: {
    query: string
    category: string
    subcategory: string
    subsubcategory: string
    tags: string
    sensitive: boolean
  }
}

export function AdvancedSearch({ isOpen, onClose, onSearch, categories, initialFilters }: AdvancedSearchProps) {
  const [query, setQuery] = useState(initialFilters.query)
  const [selectedCategory, setSelectedCategory] = useState(initialFilters.category)
  const [selectedSubcategory, setSelectedSubcategory] = useState(initialFilters.subcategory)
  const [selectedSubsubcategory, setSelectedSubsubcategory] = useState(initialFilters.subsubcategory)
  const [tags, setTags] = useState(initialFilters.tags)
  const [sensitive, setSensitive] = useState(initialFilters.sensitive)

  const currentCategory = categories.find((c) => c.id === selectedCategory)
  const currentSubcategory = currentCategory?.subcategories.find((s) => s.id === selectedSubcategory)

  const handleSearch = () => {
    onSearch({
      query,
      category: selectedCategory,
      subcategory: selectedSubcategory,
      subsubcategory: selectedSubsubcategory,
      tags,
      sensitive,
    })
    onClose()
  }

  const handleReset = () => {
    setQuery("")
    setSelectedCategory("")
    setSelectedSubcategory("")
    setSelectedSubsubcategory("")
    setTags("")
    setSensitive(false)
    onSearch({ query: "", category: "", subcategory: "", subsubcategory: "", tags: "", sensitive: false })
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>高级搜索</DialogTitle>
          <DialogDescription>使用更多条件来精确查找工具。</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="query" className="text-right">
              关键词
            </Label>
            <Input
              id="query"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              className="col-span-3"
              placeholder="工具名称、描述、URL..."
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="category" className="text-right">
              主分类
            </Label>
            <Select
              value={selectedCategory}
              onValueChange={(value) => {
                setSelectedCategory(value)
                setSelectedSubcategory("")
                setSelectedSubsubcategory("")
              }}
            >
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="选择主分类" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">所有主分类</SelectItem>
                {categories.map((cat) => (
                  <SelectItem key={cat.id} value={cat.id}>
                    {cat.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {selectedCategory && (
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="subcategory" className="text-right">
                子分类
              </Label>
              <Select
                value={selectedSubcategory}
                onValueChange={(value) => {
                  setSelectedSubcategory(value)
                  setSelectedSubsubcategory("")
                }}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="选择子分类" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有子分类</SelectItem>
                  {currentCategory?.subcategories.map((subcat) => (
                    <SelectItem key={subcat.id} value={subcat.id}>
                      {subcat.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {selectedSubcategory && (
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="subsubcategory" className="text-right">
                子子分类
              </Label>
              <Select value={selectedSubsubcategory} onValueChange={setSelectedSubsubcategory}>
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="选择子子分类" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有子子分类</SelectItem>
                  {currentSubcategory?.subsubcategories.map((subsubcat) => (
                    <SelectItem key={subsubcat.id} value={subsubcat.id}>
                      {subsubcat.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="tags" className="text-right">
              标签
            </Label>
            <Input
              id="tags"
              value={tags}
              onChange={(e) => setTags(e.target.value)}
              className="col-span-3"
              placeholder="多个标签用逗号分隔"
            />
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="sensitive" className="text-right">
              显示敏感工具
            </Label>
            <Switch id="sensitive" checked={sensitive} onCheckedChange={setSensitive} className="col-span-3" />
          </div>
        </div>
        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={handleReset}>
            重置
          </Button>
          <Button onClick={handleSearch}>搜索</Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
