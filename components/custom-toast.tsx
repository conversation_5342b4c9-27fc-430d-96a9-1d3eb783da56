"use client"

import React, { useState, useEffect } from 'react'
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react'

export interface CustomToastProps {
  id: string
  title: string
  description?: string
  type?: 'success' | 'error' | 'warning' | 'info'
  duration?: number
  onClose: (id: string) => void
}

export function CustomToast({ 
  id, 
  title, 
  description, 
  type = 'success', 
  duration = 5000, 
  onClose 
}: CustomToastProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [isLeaving, setIsLeaving] = useState(false)

  useEffect(() => {
    // 进入动画
    const timer = setTimeout(() => setIsVisible(true), 10)
    
    // 自动关闭
    const autoCloseTimer = setTimeout(() => {
      handleClose()
    }, duration)

    return () => {
      clearTimeout(timer)
      clearTimeout(autoCloseTimer)
    }
  }, [duration])

  const handleClose = () => {
    setIsLeaving(true)
    setTimeout(() => {
      onClose(id)
    }, 300) // 等待退出动画完成
  }

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />
      case 'info':
        return <Info className="h-5 w-5 text-blue-500" />
      default:
        return <CheckCircle className="h-5 w-5 text-green-500" />
    }
  }

  const getBackgroundColor = () => {
    switch (type) {
      case 'success':
        return 'bg-white dark:bg-gray-800 border-green-200 dark:border-green-800'
      case 'error':
        return 'bg-white dark:bg-gray-800 border-red-200 dark:border-red-800'
      case 'warning':
        return 'bg-white dark:bg-gray-800 border-yellow-200 dark:border-yellow-800'
      case 'info':
        return 'bg-white dark:bg-gray-800 border-blue-200 dark:border-blue-800'
      default:
        return 'bg-white dark:bg-gray-800 border-green-200 dark:border-green-800'
    }
  }

  return (
    <div
      className={`
        fixed top-4 right-4 z-[9999] max-w-sm w-full
        transform transition-all duration-300 ease-in-out
        ${isVisible && !isLeaving 
          ? 'translate-x-0 opacity-100' 
          : 'translate-x-full opacity-0'
        }
      `}
    >
      <div
        className={`
          ${getBackgroundColor()}
          border rounded-lg shadow-lg p-4
          flex items-start gap-3
        `}
      >
        {/* 图标 */}
        <div className="flex-shrink-0 mt-0.5">
          {getIcon()}
        </div>

        {/* 内容 */}
        <div className="flex-1 min-w-0">
          <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
            {title}
          </div>
          {description && (
            <div className="mt-1 text-sm text-gray-600 dark:text-gray-300">
              {description}
            </div>
          )}
        </div>

        {/* 关闭按钮 */}
        <button
          onClick={handleClose}
          className="flex-shrink-0 ml-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
        >
          <X className="h-4 w-4" />
        </button>
      </div>
    </div>
  )
}

// Toast容器组件
export interface ToastData {
  id: string
  title: string
  description?: string
  type?: 'success' | 'error' | 'warning' | 'info'
  duration?: number
}

export function CustomToastContainer() {
  const [toasts, setToasts] = useState<ToastData[]>([])

  // 添加toast的方法
  const addToast = (toast: Omit<ToastData, 'id'>) => {
    const id = Date.now().toString() + Math.random().toString(36).substr(2, 9)
    setToasts(prev => [...prev, { ...toast, id }])
  }

  // 移除toast的方法
  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id))
  }

  // 将addToast方法暴露到全局
  useEffect(() => {
    (window as any).showCustomToast = addToast
  }, [])

  return (
    <>
      {toasts.map((toast) => (
        <CustomToast
          key={toast.id}
          {...toast}
          onClose={removeToast}
        />
      ))}
    </>
  )
}
