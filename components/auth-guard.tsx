"use client"

import { useEffect, useState } from "react"
import { AccessControl } from "./access-control"
import { isAuthenticated, setAuthStatus } from "@/lib/auth-utils"

interface AuthGuardProps {
  children: React.ReactNode
}

export function AuthGuard({ children }: AuthGuardProps) {
  const [isAuth, setIsAuth] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // 检查认证状态
    const checkAuth = () => {
      const authenticated = isAuthenticated()
      setIsAuth(authenticated)
      setIsLoading(false)
    }

    checkAuth()
  }, [])

  const handleAuthenticated = () => {
    setAuthStatus()
    setIsAuth(true)
  }

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>加载中...</p>
        </div>
      </div>
    )
  }

  if (!isAuth) {
    return <AccessControl onAuthenticated={handleAuthenticated} />
  }

  return <>{children}</>
}
