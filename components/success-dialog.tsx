"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { CheckCircle } from "lucide-react"
import type { Tool } from "@/components/toolbox"

interface SuccessDialogProps {
  isOpen: boolean
  onClose: () => void
  tool: Tool | null
  getCategoryPath: (categoryId: string, subcategoryId: string, subsubcategoryId: string) => string
}

export function SuccessDialog({ isOpen, onClose, tool, getCategoryPath }: SuccessDialogProps) {
  if (!tool) return null

  const categoryPath = getCategoryPath(tool.category, tool.subcategory, tool.subsubcategory)

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader className="flex flex-col items-center text-center">
          <CheckCircle className="h-12 w-12 text-green-500 mb-4" />
          <DialogTitle className="text-2xl">添加成功！</DialogTitle>
          <DialogDescription className="text-base">
            工具 <strong>{tool.name}</strong> 已成功添加到你的工具箱。
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-2 py-4 text-sm text-muted-foreground">
          <div className="flex justify-between">
            <span>分类:</span>
            <span className="font-medium text-foreground">{categoryPath}</span>
          </div>
          <div className="flex justify-between">
            <span>标签:</span>
            <span className="font-medium text-foreground">
              {tool.tags && tool.tags.length > 0 ? tool.tags.join(", ") : "无"}
            </span>
          </div>
        </div>
        <div className="flex justify-center">
          <Button onClick={onClose}>继续添加</Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
