"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { <PERSON>ton } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"
import { Loader2 } from "lucide-react"
import { SuccessDialog } from "@/components/success-dialog"
import type { Tool, Category } from "@/components/toolbox"

interface QuickAddDialogProps {
  isOpen: boolean
  onClose: () => void
  onAddTool: (tool: Omit<Tool, "id" | "addedAt" | "sensitive">) => void
  categories: Category[]
  getCategoryPath: (categoryId: string, subcategoryId: string, subsubcategoryId: string) => string
}

export function QuickAddDialog({ isOpen, onClose, onAddTool, categories, getCategoryPath }: QuickAddDialogProps) {
  const [url, setUrl] = useState("")
  const [name, setName] = useState("")
  const [description, setDescription] = useState("")
  const [tags, setTags] = useState<string[]>([])
  const [selectedCategory, setSelectedCategory] = useState("")
  const [selectedSubcategory, setSelectedSubcategory] = useState("")
  const [selectedSubsubcategory, setSelectedSubsubcategory] = useState("")
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [showSuccessDialog, setShowSuccessDialog] = useState(false)
  const [addedTool, setAddedTool] = useState<Tool | null>(null)

  const { toast } = useToast()

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (!isOpen) {
      // Reset all states when dialog closes
      setUrl("")
      setName("")
      setDescription("")
      setTags([])
      setSelectedCategory("")
      setSelectedSubcategory("")
      setSelectedSubsubcategory("")
      setIsAnalyzing(false)
      setAddedTool(null)
    }
  }, [isOpen])

  const currentCategory = categories.find((c) => c.id === selectedCategory)
  const currentSubcategory = currentCategory?.subcategories.find((s) => s.id === selectedSubcategory)

  const handleAnalyzeUrl = async () => {
    if (!url.trim()) {
      toast({
        title: "URL 不能为空",
        description: "请输入一个有效的 URL 进行分析。",
        variant: "destructive",
      })
      return
    }

    setIsAnalyzing(true)
    try {
      const { analyzeUrl } = await import('@/lib/api-client')
      const data = await analyzeUrl(url)
      setName(data.name || "")
      setDescription(data.description || "")
      setTags(data.tags || [])
      setSelectedCategory(data.category || "")
      setSelectedSubcategory(data.subcategory || "")
      setSelectedSubsubcategory(data.subsubcategory || "")

      toast({
        title: "分析成功",
        description: "URL 信息已自动填充。",
      })
    } catch (error: any) {
      console.error("URL analysis error:", error)
      toast({
        title: "分析失败",
        description: error.message || "无法获取 URL 信息，请手动填写。",
        variant: "destructive",
      })
    } finally {
      setIsAnalyzing(false)
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!name.trim() || !url.trim() || !selectedCategory) {
      toast({
        title: "请填写必填项",
        description: "工具名称、URL 和主分类是必填项。",
        variant: "destructive",
      })
      return
    }

    const newTool: Omit<Tool, "id" | "addedAt" | "sensitive"> = {
      name: name.trim(),
      url: url.trim(),
      description: description.trim(),
      tags: tags.map((tag) => tag.trim()).filter(Boolean),
      category: selectedCategory,
      subcategory: selectedSubcategory || "",
      subsubcategory: selectedSubsubcategory || "",
    }

    onAddTool(newTool)
    setAddedTool({
      ...newTool,
      id: Date.now().toString(), // Temporary ID for success dialog
      addedAt: new Date().toISOString(),
      sensitive: false,
    })
    setShowSuccessDialog(true)
    onClose() // Close the quick add dialog
  }

  const handleSuccessDialogClose = () => {
    setShowSuccessDialog(false)
    // Optionally, keep the quick add dialog open for another entry
    // or close it completely based on user preference.
    // For now, it will remain closed as per the onClose() above.
  }

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>快速添加工具</DialogTitle>
            <DialogDescription>输入工具链接，AI 将自动分析并填充信息。</DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="url">工具链接</Label>
              <div className="flex gap-2">
                <Input
                  id="url"
                  placeholder="https://example.com"
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                  required
                  className="flex-1"
                />
                <Button type="button" onClick={handleAnalyzeUrl} disabled={isAnalyzing}>
                  {isAnalyzing ? <Loader2 className="h-4 w-4 animate-spin" /> : "分析"}
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="name">工具名称</Label>
              <Input
                id="name"
                placeholder="例如：Figma"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">描述</Label>
              <Textarea
                id="description"
                placeholder="例如：一款强大的UI/UX设计工具"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={3}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="tags">标签</Label>
              <Input
                id="tags"
                placeholder="例如：设计, 协作, UI"
                value={tags.join(", ")}
                onChange={(e) => setTags(e.target.value.split(",").map((tag) => tag.trim()))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="category">一级分类</Label>
              <Select
                value={selectedCategory}
                onValueChange={(value) => {
                  setSelectedCategory(value)
                  setSelectedSubcategory("")
                  setSelectedSubsubcategory("")
                }}
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择一级分类" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((cat) => (
                    <SelectItem key={cat.id} value={cat.id}>
                      {cat.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            {selectedCategory && (
              <div className="space-y-2">
                <Label htmlFor="subcategory">二级分类</Label>
                <Select
                  value={selectedSubcategory}
                  onValueChange={(value) => {
                    setSelectedSubcategory(value)
                    setSelectedSubsubcategory("")
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择二级分类" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">无二级分类</SelectItem>
                    {currentCategory?.subcategories.map((subcat) => (
                      <SelectItem key={subcat.id} value={subcat.id}>
                        {subcat.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
            {selectedSubcategory && (
              <div className="space-y-2">
                <Label htmlFor="subsubcategory">三级分类</Label>
                <Select value={selectedSubsubcategory} onValueChange={setSelectedSubsubcategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择三级分类" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">无三级分类</SelectItem>
                    {currentSubcategory?.subsubcategories.map((subsubcat) => (
                      <SelectItem key={subsubcat.id} value={subsubcat.id}>
                        {subsubcat.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
            <div className="flex justify-end gap-2 pt-4">
              <Button type="button" variant="outline" onClick={onClose}>
                取消
              </Button>
              <Button type="submit">
                添加工具
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      <SuccessDialog
        isOpen={showSuccessDialog}
        onClose={handleSuccessDialogClose}
        tool={addedTool}
        getCategoryPath={getCategoryPath}
      />
    </>
  )
}
