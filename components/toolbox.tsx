"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Plus, Settings, Upload, Search, Menu } from "lucide-react"
import { QuickAddDialog } from "@/components/quick-add-dialog"
import { CategoryManager } from "@/components/category-manager"
import { EnhancedDataManagement } from "@/components/enhanced-data-management"
import { ModeToggle } from "@/components/mode-toggle"
import { SyncStatus } from "@/components/sync-status"
import { Logo } from "@/components/logo"

export interface Tool {
  id: string
  name: string
  url: string
  description: string
  tags: string[]
  category: string
  subcategory: string
  subsubcategory: string
  addedAt: string // ISO string date
  sensitive: boolean
}

export interface Category {
  id: string
  name: string
  subcategories: {
    id: string
    name: string
    subsubcategories: {
      id: string
      name: string
    }[]
  }[]
}

interface ToolboxProps {
  tools: Tool[]
  categories: Category[]
  onAddTool: (tool: Omit<Tool, "id" | "addedAt" | "sensitive">) => void
  onUpdateTool: (tool: Tool) => void
  onDeleteTool: (id: string) => void
  onUpdateCategories: (categories: Category[]) => void
  onImportData: (data: { tools: Tool[]; categories: Category[] }) => void
  onExportData: () => { tools: Tool[]; categories: Category[] }
  getCategoryPath: (categoryId: string, subcategoryId: string, subsubcategoryId: string) => string
  searchQuery: string
  onSearch: (query: string) => void
}

export function Toolbox({
  tools,
  categories,
  onAddTool,
  onUpdateTool,
  onDeleteTool,
  onUpdateCategories,
  onImportData,
  onExportData,
  getCategoryPath,
  searchQuery,
  onSearch,
}: ToolboxProps) {
  const [quickAddDialogOpen, setQuickAddDialogOpen] = useState(false)
  const [categoryManagerOpen, setCategoryManagerOpen] = useState(false)
  const [dataManagementOpen, setDataManagementOpen] = useState(false)

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center gap-3">
          <Logo size={24} className="text-primary" />
          <h1 className="text-xl font-bold">ToolMaster</h1>
        </div>
        <div className="flex items-center gap-2">
          {/* Desktop Search Input */}
          <div className="relative hidden sm:block w-64">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
            <Input
              placeholder="搜索工具..."
              value={searchQuery}
              onChange={(e) => onSearch(e.target.value)}
              className="pl-10"
            />
          </div>
          {/* 快速添加按钮 */}
          <Button variant="ghost" size="sm" onClick={() => setQuickAddDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">快速添加</span>
          </Button>

          {/* 分类管理按钮 */}
          <Button variant="ghost" size="sm" onClick={() => setCategoryManagerOpen(true)}>
            <Settings className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">分类管理</span>
          </Button>

          {/* 数据管理按钮 */}
          <Button variant="ghost" size="sm" onClick={() => setDataManagementOpen(true)}>
            <Upload className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">数据管理</span>
          </Button>

          {/* 同步状态指示器 */}
          <SyncStatus />

          {/* 主题切换按钮 */}
          <ModeToggle />

          {/* Mobile sidebar toggle - hidden on desktop */}
          <Button variant="ghost" size="icon" className="md:hidden">
            <Menu className="h-5 w-5" />
            <span className="sr-only">打开侧边栏</span>
          </Button>
        </div>
      </div>

      <QuickAddDialog
        isOpen={quickAddDialogOpen}
        onClose={() => setQuickAddDialogOpen(false)}
        onAddTool={onAddTool}
        categories={categories}
        getCategoryPath={getCategoryPath}
      />
      <CategoryManager
        isOpen={categoryManagerOpen}
        onClose={() => setCategoryManagerOpen(false)}
        categories={categories}
        onUpdateCategories={onUpdateCategories}
      />
      <EnhancedDataManagement
        isOpen={dataManagementOpen}
        onClose={() => setDataManagementOpen(false)}
        onImportData={onImportData}
        onExportData={onExportData}
        tools={tools}
        categories={categories}
      />
    </header>
  )
}
