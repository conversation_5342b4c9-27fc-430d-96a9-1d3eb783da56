"use client"

import React from 'react'

interface LogoProps {
  className?: string
  size?: number
}

export function Logo({ className = "", size = 24 }: LogoProps) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      {/* 背景圆形 */}
      <circle
        cx="16"
        cy="16"
        r="15"
        fill="url(#logoGradient)"
        stroke="#2563eb"
        strokeWidth="1"
      />

      {/* 工具箱主体 */}
      <rect
        x="8"
        y="12"
        width="16"
        height="10"
        rx="2"
        fill="#1e40af"
        stroke="#1d4ed8"
        strokeWidth="0.5"
      />

      {/* 工具箱手柄 */}
      <rect
        x="14"
        y="10"
        width="4"
        height="2"
        rx="1"
        fill="#374151"
        stroke="#4b5563"
        strokeWidth="0.5"
      />

      {/* 工具箱锁扣 */}
      <rect
        x="15"
        y="14"
        width="2"
        height="1"
        rx="0.5"
        fill="#fbbf24"
      />

      {/* 锤子工具 */}
      <g transform="translate(10, 6)">
        {/* 锤子手柄 */}
        <rect
          x="5"
          y="2"
          width="1"
          height="8"
          rx="0.5"
          fill="#8b5cf6"
        />
        {/* 锤子头部 */}
        <rect
          x="3"
          y="1"
          width="5"
          height="2"
          rx="0.5"
          fill="#7c3aed"
        />
      </g>

      {/* 扳手工具 */}
      <g transform="translate(18, 7)">
        <path
          d="M1 2L3 4L2 5L0 3Z"
          fill="#10b981"
        />
        <circle
          cx="2"
          cy="1"
          r="1"
          fill="none"
          stroke="#059669"
          strokeWidth="0.5"
        />
      </g>

      {/* 螺丝刀工具 */}
      <g transform="translate(12, 8)">
        <rect
          x="0"
          y="0"
          width="0.8"
          height="6"
          rx="0.4"
          fill="#f59e0b"
        />
        <rect
          x="-0.2"
          y="5"
          width="1.2"
          height="2"
          rx="0.2"
          fill="#d97706"
        />
      </g>

      {/* 齿轮装饰 */}
      <g transform="translate(20, 16)">
        <circle
          cx="0"
          cy="0"
          r="2"
          fill="#ef4444"
          opacity="0.8"
        />
        <circle
          cx="0"
          cy="0"
          r="1"
          fill="#dc2626"
        />
        {/* 齿轮齿 */}
        <rect x="-0.3" y="-2.5" width="0.6" height="1" fill="#ef4444" />
        <rect x="-0.3" y="1.5" width="0.6" height="1" fill="#ef4444" />
        <rect x="1.5" y="-0.3" width="1" height="0.6" fill="#ef4444" />
        <rect x="-2.5" y="-0.3" width="1" height="0.6" fill="#ef4444" />
      </g>

      {/* 火花效果 */}
      <g opacity="0.7">
        <circle cx="22" cy="10" r="0.5" fill="#fbbf24" />
        <circle cx="24" cy="12" r="0.3" fill="#f59e0b" />
        <circle cx="26" cy="14" r="0.4" fill="#fbbf24" />
        <path d="M23 8L24 9L23 10L22 9Z" fill="#f59e0b" />
      </g>

      {/* 渐变定义 */}
      <defs>
        <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#3b82f6" />
          <stop offset="50%" stopColor="#1d4ed8" />
          <stop offset="100%" stopColor="#1e40af" />
        </linearGradient>
      </defs>
    </svg>
  )
}
