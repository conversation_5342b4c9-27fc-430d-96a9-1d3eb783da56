"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separa<PERSON> } from "@/components/ui/separator"
import { useToast } from "@/components/ui/use-toast"
import { useCustomToast } from "@/hooks/use-custom-toast"
import {
  Upload,
  Download,
  Trash2,
  RefreshCw,
  FileText,
  BarChart3,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Loader2,
  Copy,
  Merge,
  Shield
} from "lucide-react"
import type { Tool, Category } from "@/components/toolbox"
import { CleanupService, BatchImportService } from "@/lib/cleanup-service"
import { StatisticsService, StatisticsData } from "@/lib/statistics-service"
import { ExtendedDatabase } from "@/lib/database-extended"
import { BookmarkExporter } from "@/lib/bookmark-export"

interface EnhancedDataManagementProps {
  isOpen: boolean
  onClose: () => void
  onImportData: (data: { tools: Tool[]; categories: Category[] }) => void
  onExportData: () => { tools: Tool[]; categories: Category[] }
  tools: Tool[]
  categories: Category[]
}

export function EnhancedDataManagement({
  isOpen,
  onClose,
  onImportData,
  onExportData,
  tools,
  categories
}: EnhancedDataManagementProps) {
  const [activeTab, setActiveTab] = useState("import")
  const [importText, setImportText] = useState("")
  const [importFile, setImportFile] = useState<File | null>(null)
  const [importTasks, setImportTasks] = useState<any[]>([])
  const [cleanupResults, setCleanupResults] = useState<any[]>([])
  const [operationLogs, setOperationLogs] = useState<any[]>([])
  const [statistics, setStatistics] = useState<StatisticsData | null>(null)
  const [isTextImportLoading, setIsTextImportLoading] = useState(false)
  const [isFileImportLoading, setIsFileImportLoading] = useState(false)
  const [isCleanupLoading, setIsCleanupLoading] = useState(false)
  const [loadingOperation, setLoadingOperation] = useState("")

  // 确认对话框状态
  const [confirmDialog, setConfirmDialog] = useState<{
    isOpen: boolean
    title: string
    description: string
    onConfirm: () => void
    confirmText?: string
    variant?: 'default' | 'destructive'
  }>({
    isOpen: false,
    title: '',
    description: '',
    onConfirm: () => {},
    confirmText: '确认',
    variant: 'default'
  })

  const { toast } = useToast()
  const customToast = useCustomToast()

  // 加载数据
  useEffect(() => {
    if (isOpen) {
      loadImportTasks()
      loadOperationLogs()
      loadCleanupReports()
      loadStatistics()
    }
  }, [isOpen, activeTab])

  const loadImportTasks = async () => {
    try {
      const tasks = await ExtendedDatabase.getImportTasks(10)
      setImportTasks(tasks)
    } catch (error) {
      console.error('加载导入任务失败:', error)
    }
  }

  const loadOperationLogs = async () => {
    try {
      const logs = await ExtendedDatabase.getOperationLogs(50)
      setOperationLogs(logs)
    } catch (error) {
      console.error('加载操作日志失败:', error)
    }
  }

  const loadCleanupReports = async () => {
    try {
      const reports = await ExtendedDatabase.getCleanupReports(20)
      // 转换数据格式以匹配现有的cleanupResults结构
      const formattedReports = reports.map(report => ({
        operationType: report.operation_type,
        originalCount: report.original_count,
        processedCount: report.processed_count,
        removedCount: report.removed_count,
        details: report.details,
        timestamp: report.created_at
      }))
      setCleanupResults(formattedReports)
    } catch (error) {
      console.error('加载清理报告失败:', error)
    }
  }

  const loadStatistics = async () => {
    try {
      // 先尝试获取缓存的统计数据
      let stats = await StatisticsService.getCachedStatistics()
      if (!stats) {
        // 如果没有缓存，重新计算
        stats = await StatisticsService.calculateStatistics(tools, categories)
      }
      setStatistics(stats)
    } catch (error) {
      console.error('加载统计数据失败:', error)
      // 设置默认统计数据以避免错误
      const defaultStats = {
        overview: {
          totalTools: tools.length,
          totalCategories: categories.length,
          todayAdded: 0,
          yesterdayAdded: 0,
          thisMonthAdded: 0,
          thisWeekAdded: 0,
          averagePerDay: 0,
          growthRate: 0
        },
        categories: [],
        tags: [],
        timeline: [],
        trends: {
          dailyGrowth: [],
          weeklyGrowth: [],
          monthlyGrowth: [],
          popularCategories: [],
          emergingTags: []
        },
        quality: {
          withDescription: 0,
          withTags: 0,
          averageTagsPerTool: 0,
          sensitiveTools: 0,
          duplicateUrls: 0,
          invalidUrls: 0
        }
      }
      setStatistics(defaultStats)
    }
  }

  // ==================== 批量导入功能 ====================

  const handleTextImport = async () => {
    if (!importText.trim()) {
      customToast.error("导入失败", "请输入要导入的内容")
      return
    }

    setIsTextImportLoading(true)
    setLoadingOperation("正在分析文本内容...")

    // 立即显示导入中提示
    console.log('显示导入中提示...')
    customToast.info("导入中，请稍等...", "正在分析文本内容并创建导入任务")

    try {
      console.log('开始文本导入...')
      const taskId = await BatchImportService.processTextImport(importText, "文本批量导入")
      console.log('导入任务已创建，ID:', taskId)

      // 显示任务创建成功提示
      customToast.success("导入任务已创建", "正在后台处理，请稍后查看结果")

      setImportText("")
      await loadImportTasks()

      // 立即开始监控任务状态
      monitorImportTask(taskId, 'text')
    } catch (error) {
      console.error('文本导入失败:', error)
      customToast.error("导入失败", error instanceof Error ? error.message : "未知错误")
    } finally {
      setIsTextImportLoading(false)
      setLoadingOperation("")
    }
  }

  const handleFileImport = async () => {
    if (!importFile) {
      customToast.error("导入失败", "请选择要导入的文件")
      return
    }

    // 检查是否是备份文件
    const isBackupFile = importFile.name.includes('toolmaster-backup') ||
                        importFile.name.includes('ToolMaster') ||
                        importFile.type === 'application/json'

    if (isBackupFile && importFile.type === 'application/json') {
      // 处理备份文件恢复
      await handleBackupRestore()
      return
    }

    setIsFileImportLoading(true)
    setLoadingOperation("正在处理文件...")

    // 立即显示导入中提示
    console.log('显示文件导入中提示...')
    customToast.info("导入中，请稍等...", "正在处理文件并创建导入任务")

    try {
      console.log('开始文件导入...')
      const taskId = await BatchImportService.processFileImport(importFile)
      console.log('导入任务已创建，ID:', taskId)

      // 显示任务创建成功提示
      customToast.success("导入任务已创建", "正在后台处理，请稍后查看结果")

      setImportFile(null)
      await loadImportTasks()

      // 立即开始监控任务状态
      monitorImportTask(taskId, 'file')
    } catch (error) {
      console.error('文件导入失败:', error)
      customToast.error("导入失败", error instanceof Error ? error.message : "未知错误")
    } finally {
      setIsFileImportLoading(false)
      setLoadingOperation("")
    }
  }

  // 处理备份文件恢复
  const handleBackupRestore = async () => {
    if (!importFile) return

    setIsFileImportLoading(true)
    setLoadingOperation("正在恢复备份数据...")

    try {
      const fileContent = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = (e) => resolve(e.target?.result as string)
        reader.onerror = () => reject(new Error('文件读取失败'))
        reader.readAsText(importFile, 'utf-8')
      })

      const backupData = JSON.parse(fileContent)

      // 验证备份文件格式
      if (!backupData.tools || !Array.isArray(backupData.tools) ||
          !backupData.categories || !Array.isArray(backupData.categories)) {
        throw new Error('备份文件格式不正确')
      }

      // 记录操作日志
      await ExtendedDatabase.createOperationLog(
        '从备份文件恢复数据',
        'restore',
        'backup',
        undefined,
        {
          fileName: importFile.name,
          toolsCount: backupData.tools.length,
          categoriesCount: backupData.categories.length,
          backupVersion: backupData.version,
          backupDate: backupData.exportedAt
        },
        'success'
      )

      // 重新加载操作日志
      await loadOperationLogs()

      customToast.success(
        "备份恢复成功",
        `已恢复 ${backupData.tools.length} 个工具和 ${backupData.categories.length} 个分类`
      )

      // 调用父组件的导入函数
      if (onImportData) {
        onImportData(backupData)
      }

      setImportFile(null)

    } catch (error) {
      console.error('备份恢复失败:', error)

      // 记录错误日志
      await ExtendedDatabase.createOperationLog(
        '从备份文件恢复数据',
        'restore',
        'backup',
        undefined,
        {
          fileName: importFile.name,
          error: error instanceof Error ? error.message : '未知错误'
        },
        'error',
        error instanceof Error ? error.message : '未知错误'
      )

      customToast.error("备份恢复失败", error instanceof Error ? error.message : "未知错误")
    } finally {
      setIsFileImportLoading(false)
      setLoadingOperation("")
    }
  }

  // ==================== 数据导出功能 ====================

  const handleExportData = async () => {
    try {
      const data = onExportData()
      const jsonString = JSON.stringify(data, null, 2)

      // 创建下载链接
      const blob = new Blob([jsonString], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `toolmaster-export-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      // 记录操作日志
      await ExtendedDatabase.createOperationLog(
        '导出JSON数据文件',
        'export',
        'data',
        undefined,
        {
          exportType: 'json',
          toolsCount: data.tools?.length || 0,
          categoriesCount: data.categories?.length || 0,
          fileSize: blob.size,
          fileName: `toolmaster-export-${new Date().toISOString().split('T')[0]}.json`
        },
        'success'
      )

      // 重新加载操作日志
      await loadOperationLogs()

      toast({
        title: "导出成功",
        description: "数据已下载到本地",
      })
    } catch (error) {
      // 记录错误日志
      await ExtendedDatabase.createOperationLog(
        '导出JSON数据文件',
        'export',
        'data',
        undefined,
        {
          exportType: 'json',
          error: error instanceof Error ? error.message : '未知错误'
        },
        'error',
        error instanceof Error ? error.message : '未知错误'
      )

      toast({
        title: "导出失败",
        description: "生成导出数据时发生错误",
        variant: "destructive"
      })
    }
  }

  // 导出为浏览器书签
  const handleExportBookmarks = async () => {
    try {
      BookmarkExporter.downloadBookmarks(tools, categories)

      const stats = BookmarkExporter.getExportStats(tools, categories)

      // 记录操作日志
      await ExtendedDatabase.createOperationLog(
        '导出浏览器书签文件',
        'export',
        'bookmarks',
        undefined,
        {
          exportType: 'bookmarks',
          toolsCount: stats.totalTools,
          categoriesCount: stats.totalCategories,
          subcategoriesCount: stats.totalSubcategories,
          subsubcategoriesCount: stats.totalSubsubcategories,
          fileName: `toolmaster-bookmarks-${new Date().toISOString().split('T')[0]}.html`
        },
        'success'
      )

      // 重新加载操作日志
      await loadOperationLogs()

      toast({
        title: "书签导出成功",
        description: `已导出 ${stats.totalTools} 个工具，分布在 ${stats.totalCategories} 个分类中`,
      })
    } catch (error) {
      console.error('导出书签失败:', error)

      // 记录错误日志
      await ExtendedDatabase.createOperationLog(
        '导出浏览器书签文件',
        'export',
        'bookmarks',
        undefined,
        {
          exportType: 'bookmarks',
          error: error instanceof Error ? error.message : '未知错误'
        },
        'error',
        error instanceof Error ? error.message : '未知错误'
      )

      toast({
        title: "导出失败",
        description: "生成书签文件时发生错误",
        variant: "destructive"
      })
    }
  }

  // ==================== 数据清理功能 ====================

  const handleRemoveDuplicates = () => {
    setConfirmDialog({
      isOpen: true,
      title: "确认移除重复工具",
      description: "此操作将移除所有重复的工具（基于URL匹配）。此操作不可撤销，确定要继续吗？",
      confirmText: "移除重复",
      variant: 'destructive',
      onConfirm: async () => {
        setConfirmDialog(prev => ({ ...prev, isOpen: false }))
        setIsCleanupLoading(true)
        setLoadingOperation("正在移除重复工具...")

        try {
          const result = await CleanupService.removeDuplicateTools(tools)
          setCleanupResults(prev => [result, ...prev])

          toast({
            title: "清理完成",
            description: `移除了 ${result.removedCount} 个重复工具`,
          })

          // 重新加载数据而不是刷新页面
          await loadOperationLogs()
          await loadCleanupReports()
          await loadStatistics()
        } catch (error) {
          toast({
            title: "清理失败",
            description: error instanceof Error ? error.message : "未知错误",
            variant: "destructive"
          })
        } finally {
          setIsCleanupLoading(false)
          setLoadingOperation("")
        }
      }
    })
  }

  const handleRemoveInvalid = () => {
    setConfirmDialog({
      isOpen: true,
      title: "确认删除无效工具",
      description: "此操作将删除所有无效的工具（无法访问的URL）。此操作不可撤销，确定要继续吗？",
      confirmText: "删除无效",
      variant: 'destructive',
      onConfirm: async () => {
        setConfirmDialog(prev => ({ ...prev, isOpen: false }))
        setIsCleanupLoading(true)
        setLoadingOperation("正在删除无效工具...")

        try {
          const result = await CleanupService.removeInvalidTools(tools)
          setCleanupResults(prev => [result, ...prev])

          toast({
            title: "清理完成",
            description: `删除了 ${result.removedCount} 个无效工具`,
          })

          // 重新加载数据而不是刷新页面
          await loadOperationLogs()
          await loadCleanupReports()
          await loadStatistics()
        } catch (error) {
          toast({
            title: "清理失败",
            description: error instanceof Error ? error.message : "未知错误",
            variant: "destructive"
          })
        } finally {
          setIsCleanupLoading(false)
          setLoadingOperation("")
        }
      }
    })
  }

  const handleMergeTools = () => {
    setConfirmDialog({
      isOpen: true,
      title: "确认合并重复工具",
      description: "此操作将智能合并重复工具的信息，保留最完整的数据。此操作不可撤销，确定要继续吗？",
      confirmText: "合并工具",
      variant: 'default',
      onConfirm: async () => {
        setConfirmDialog(prev => ({ ...prev, isOpen: false }))
        setIsCleanupLoading(true)
        setLoadingOperation("正在合并重复工具信息...")

        try {
          const result = await CleanupService.mergeToolInfo(tools)
          setCleanupResults(prev => [result, ...prev])

          toast({
            title: "合并完成",
            description: `合并了 ${result.mergedItems?.length || 0} 组重复工具`,
          })

          // 重新加载数据而不是刷新页面
          await loadOperationLogs()
          await loadCleanupReports()
          await loadStatistics()
        } catch (error) {
          toast({
            title: "合并失败",
            description: error instanceof Error ? error.message : "未知错误",
            variant: "destructive"
          })
        } finally {
          setIsCleanupLoading(false)
          setLoadingOperation("")
        }
      }
    })
  }

  const handleFullCleanup = () => {
    setConfirmDialog({
      isOpen: true,
      title: "确认执行全面清理",
      description: "此操作将执行所有清理操作：移除重复工具、删除无效工具、合并重复信息。这是一个综合性操作，不可撤销，确定要继续吗？",
      confirmText: "全面清理",
      variant: 'destructive',
      onConfirm: async () => {
        setConfirmDialog(prev => ({ ...prev, isOpen: false }))
        setIsCleanupLoading(true)
        setLoadingOperation("正在执行全面清理...")

        try {
          const results = await CleanupService.fullCleanup(tools)
          setCleanupResults(prev => [...results, ...prev])

          const totalRemoved = results.reduce((sum, result) => sum + result.removedCount, 0)
          toast({
            title: "全面清理完成",
            description: `总共处理了 ${totalRemoved} 个问题`,
          })

          // 重新加载数据而不是刷新页面
          await loadOperationLogs()
          await loadCleanupReports()
          await loadStatistics()
        } catch (error) {
          toast({
            title: "清理失败",
            description: error instanceof Error ? error.message : "未知错误",
            variant: "destructive"
          })
        } finally {
          setIsCleanupLoading(false)
          setLoadingOperation("")
        }
      }
    })
  }

  // ==================== 任务监控功能 ====================

  const monitorImportTask = async (taskId: string, importType: 'text' | 'file') => {
    console.log(`开始监控导入任务: ${taskId}, 类型: ${importType}`)

    const checkTaskStatus = async () => {
      try {
        const task = await BatchImportService.getImportTaskStatus(taskId)
        console.log(`任务状态检查: ${taskId}`, task)

        if (!task) {
          console.log('任务不存在，停止监控')
          return
        }

        if (task.status === 'completed') {
          const summary = task.result_summary || {}
          const importTypeText = importType === 'text' ? '文本导入' : '文件导入'

          console.log(`${importTypeText}已完成，窗口状态: ${isOpen ? '打开' : '关闭'}`)

          // 显示完成提示 - 无论窗口是否打开都显示相同的提示
          customToast.success(
            `${importTypeText}已完成`,
            `成功导入 ${summary.success || 0} 个工具，失败 ${summary.failed || 0} 个，重复 ${summary.duplicate || 0} 个`
          )

          await loadImportTasks()
          return
        } else if (task.status === 'failed') {
          const importTypeText = importType === 'text' ? '文本导入' : '文件导入'
          console.log(`${importTypeText}失败`)

          customToast.error(
            `${importTypeText}失败`,
            task.error_details?.message || "导入过程中发生错误"
          )

          await loadImportTasks()
          return
        } else if (task.status === 'processing') {
          console.log('任务处理中，继续监控...')
          // 继续监控
          setTimeout(checkTaskStatus, 2000)
        } else {
          console.log(`未知任务状态: ${task.status}`)
          // 对于其他状态，也继续监控
          setTimeout(checkTaskStatus, 2000)
        }
      } catch (error) {
        console.error('监控任务状态失败:', error)
        // 出错时也继续监控，但增加延迟
        setTimeout(checkTaskStatus, 5000)
      }
    }

    // 立即开始第一次检查，然后延迟后续检查
    setTimeout(checkTaskStatus, 1000)
  }

  // ==================== 操作日志功能 ====================

  const handleClearLogs = async () => {
    try {
      await ExtendedDatabase.clearOperationLogs()
      setOperationLogs([])

      customToast.success("清理完成", "操作日志已清空")
    } catch (error) {
      customToast.error("清理失败", "清理操作日志时发生错误")
    }
  }

  return (
    <>
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>数据管理中心</DialogTitle>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="import">批量导入</TabsTrigger>
            <TabsTrigger value="export">数据导出</TabsTrigger>
            <TabsTrigger value="cleanup">数据清理</TabsTrigger>
            <TabsTrigger value="logs">操作日志</TabsTrigger>
            <TabsTrigger value="statistics">数据统计</TabsTrigger>
          </TabsList>

          {/* 批量导入标签页 */}
          <TabsContent value="import" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* 文本导入 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    文本导入
                  </CardTitle>
                  <CardDescription>
                    直接输入工具信息，支持URL列表、工具名称等格式
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Textarea
                    placeholder={`请输入工具信息，支持以下格式：
1. URL列表（每行一个）
2. 工具名称列表
3. 混合格式内容
4. 书签导出内容`}
                    value={importText}
                    onChange={(e) => setImportText(e.target.value)}
                    rows={8}
                  />
                  <Button
                    onClick={handleTextImport}
                    disabled={isTextImportLoading || !importText.trim()}
                    className="w-full"
                  >
                    {isTextImportLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        分析中...
                      </>
                    ) : (
                      <>
                        <Upload className="mr-2 h-4 w-4" />
                        开始导入
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>

              {/* 文件导入 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Upload className="h-4 w-4" />
                    文件导入
                  </CardTitle>
                  <CardDescription>
                    上传文件进行批量导入，支持 TXT、JSON、HTML 等格式。
                    <br />
                    <span className="text-blue-600 font-medium">特别支持：ToolMaster备份文件恢复</span>
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <Input
                      type="file"
                      accept=".txt,.json,.html,.csv"
                      onChange={(e) => setImportFile(e.target.files?.[0] || null)}
                      className="hidden"
                      id="file-upload"
                    />
                    <Label htmlFor="file-upload" className="cursor-pointer">
                      <div className="space-y-2">
                        <Upload className="mx-auto h-8 w-8 text-gray-400" />
                        <div className="text-sm text-gray-600">
                          点击选择文件或拖拽文件到此处
                        </div>
                        {importFile && (
                          <div className="text-sm font-medium text-blue-600">
                            已选择: {importFile.name}
                          </div>
                        )}
                      </div>
                    </Label>
                  </div>
                  <Button
                    onClick={handleFileImport}
                    disabled={isFileImportLoading || !importFile}
                    className="w-full"
                  >
                    {isFileImportLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        处理中...
                      </>
                    ) : (
                      <>
                        <Upload className="mr-2 h-4 w-4" />
                        上传并导入
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* 导入任务列表 */}
            <Card>
              <CardHeader>
                <CardTitle>导入任务历史</CardTitle>
                <CardDescription>查看最近的批量导入任务状态</CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-64">
                  {importTasks.length === 0 ? (
                    <div className="text-center text-gray-500 py-8">
                      暂无导入任务
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {importTasks.map((task) => (
                        <div key={task.id} className="border rounded-lg p-3">
                          <div className="flex items-center justify-between mb-2">
                            <div className="font-medium">{task.task_name}</div>
                            <Badge variant={
                              task.status === 'completed' ? 'default' :
                              task.status === 'failed' ? 'destructive' :
                              task.status === 'processing' ? 'secondary' : 'outline'
                            }>
                              {task.status === 'completed' ? '已完成' :
                               task.status === 'failed' ? '失败' :
                               task.status === 'processing' ? '处理中' : '等待中'}
                            </Badge>
                          </div>
                          {task.status === 'processing' && (
                            <Progress value={task.progress_percentage || 0} className="mb-2" />
                          )}
                          <div className="text-sm text-gray-600 grid grid-cols-2 gap-2">
                            <div>总数: {task.total_items || 0}</div>
                            <div>成功: {task.success_items || 0}</div>
                            <div>失败: {task.failed_items || 0}</div>
                            <div>重复: {task.duplicate_items || 0}</div>
                          </div>
                          <div className="text-xs text-gray-400 mt-1">
                            {new Date(task.created_at).toLocaleString()}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 数据导出标签页 */}
          <TabsContent value="export" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* JSON 数据导出 */}
              <Card className="h-fit">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Download className="h-4 w-4" />
                    JSON 数据导出
                  </CardTitle>
                  <CardDescription>
                    导出所有工具和分类数据为 JSON 文件
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <div className="flex items-center gap-2 text-blue-800 mb-2">
                      <CheckCircle className="h-4 w-4" />
                      <span className="font-medium text-sm">导出内容：</span>
                    </div>
                    <ul className="text-xs text-blue-700 space-y-1 ml-6">
                      <li>• 所有工具数据（{tools.length} 个工具）</li>
                      <li>• 完整分类结构（{categories.length} 个分类）</li>
                      <li>• 导出时间戳和版本信息</li>
                      <li>• 数据完整性校验信息</li>
                    </ul>
                  </div>

                  <Button onClick={handleExportData} className="w-full" size="lg">
                    <Download className="mr-2 h-4 w-4" />
                    下载 JSON 数据文件
                  </Button>

                  <div className="text-xs text-gray-500 text-center">
                    可在其他 ToolMaster 实例中导入使用
                  </div>
                </CardContent>
              </Card>

              {/* 浏览器书签导出 */}
              <Card className="h-fit">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    浏览器书签导出
                  </CardTitle>
                  <CardDescription>
                    导出为浏览器书签格式（HTML）
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                    <div className="flex items-center gap-2 text-green-800 mb-2">
                      <CheckCircle className="h-4 w-4" />
                      <span className="font-medium text-sm">书签特点：</span>
                    </div>
                    <ul className="text-xs text-green-700 space-y-1 ml-6">
                      <li>• 按三级分类结构组织</li>
                      <li>• 保持原有的分类层次关系</li>
                      <li>• 兼容所有主流浏览器</li>
                      <li>• 便于在其他设备上快速访问</li>
                    </ul>
                  </div>

                  <div className="bg-amber-50 border border-amber-200 rounded-lg p-2">
                    <div className="flex items-center gap-2 text-amber-800 mb-1">
                      <AlertTriangle className="h-3 w-3" />
                      <span className="font-medium text-xs">使用说明：</span>
                    </div>
                    <p className="text-xs text-amber-700">
                      下载后可通过浏览器的"导入书签"功能导入
                    </p>
                  </div>

                  <Button onClick={handleExportBookmarks} className="w-full" size="lg" variant="outline">
                    <FileText className="mr-2 h-4 w-4" />
                    下载浏览器书签文件
                  </Button>

                  <div className="text-xs text-gray-500 text-center">
                    符合 Netscape 书签格式标准
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 导出说明 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-sm">
                  <AlertTriangle className="h-4 w-4" />
                  导出说明
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <h4 className="font-medium text-blue-600 mb-2">JSON 格式用途：</h4>
                    <ul className="text-xs text-gray-600 space-y-1">
                      <li>• 数据备份和恢复</li>
                      <li>• 跨平台数据迁移</li>
                      <li>• 开发和测试环境同步</li>
                      <li>• 数据分析和处理</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-medium text-green-600 mb-2">书签格式用途：</h4>
                    <ul className="text-xs text-gray-600 space-y-1">
                      <li>• 浏览器直接访问工具</li>
                      <li>• 跨设备快速同步</li>
                      <li>• 离线工具链接访问</li>
                      <li>• 团队工具集分享</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 数据清理标签页 */}
          <TabsContent value="cleanup" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* 清理操作 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <RefreshCw className="h-4 w-4" />
                    数据清理操作
                  </CardTitle>
                  <CardDescription>
                    清理重复、无效或不完整的工具数据
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button
                    onClick={handleRemoveDuplicates}
                    disabled={isCleanupLoading}
                    variant="outline"
                    className="w-full justify-start"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    移除重复工具
                    <Badge variant="secondary" className="ml-auto">
                      基于URL匹配
                    </Badge>
                  </Button>

                  <Button
                    onClick={handleRemoveInvalid}
                    disabled={isCleanupLoading}
                    variant="outline"
                    className="w-full justify-start"
                  >
                    <XCircle className="mr-2 h-4 w-4" />
                    删除无效工具
                    <Badge variant="secondary" className="ml-auto">
                      URL检查
                    </Badge>
                  </Button>

                  <Button
                    onClick={handleMergeTools}
                    disabled={isCleanupLoading}
                    variant="outline"
                    className="w-full justify-start"
                  >
                    <Merge className="mr-2 h-4 w-4" />
                    合并重复信息
                    <Badge variant="secondary" className="ml-auto">
                      AI智能合并
                    </Badge>
                  </Button>

                  <Separator />

                  <Button
                    onClick={handleFullCleanup}
                    disabled={isCleanupLoading}
                    className="w-full"
                  >
                    {isCleanupLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        {loadingOperation}
                      </>
                    ) : (
                      <>
                        <Shield className="mr-2 h-4 w-4" />
                        全面清理
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>

              {/* 清理报告 */}
              <Card>
                <CardHeader>
                  <CardTitle>清理报告</CardTitle>
                  <CardDescription>查看最近的数据清理结果</CardDescription>
                </CardHeader>
                <CardContent>
                  <ScrollArea className="h-64">
                    {cleanupResults.length === 0 ? (
                      <div className="text-center text-gray-500 py-8">
                        暂无清理记录
                      </div>
                    ) : (
                      <div className="space-y-3">
                        {cleanupResults.map((result, index) => (
                          <div key={index} className="border rounded-lg p-3">
                            <div className="flex items-center justify-between mb-2">
                              <div className="font-medium">
                                {result.operationType === 'remove_duplicates' ? '移除重复' :
                                 result.operationType === 'remove_invalid' ? '删除无效' :
                                 result.operationType === 'merge_tags' ? '合并信息' : '全面清理'}
                              </div>
                              <Badge variant="outline">
                                {result.operationType === 'merge_tags'
                                  ? `合并${result.details?.mergedGroups?.length || 0}组`
                                  : `-${result.removedCount}`
                                }
                              </Badge>
                            </div>
                            <div className="text-sm text-gray-600">
                              原始: {result.originalCount} → 处理后: {result.processedCount}
                            </div>
                            {result.details?.processingTimeMs && (
                              <div className="text-xs text-gray-400 mt-1">
                                耗时: {result.details.processingTimeMs}ms
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </ScrollArea>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* 操作日志标签页 */}
          <TabsContent value="logs" className="space-y-4">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      操作日志
                    </CardTitle>
                    <CardDescription>
                      查看系统操作历史记录
                    </CardDescription>
                  </div>
                  <Button
                    onClick={handleClearLogs}
                    variant="outline"
                    size="sm"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    清空日志
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-96">
                  {operationLogs.length === 0 ? (
                    <div className="text-center text-gray-500 py-8">
                      暂无操作记录
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {operationLogs.map((log) => (
                        <div key={log.id} className="border rounded-lg p-3 hover:bg-gray-50">
                          <div className="flex items-center justify-between mb-1">
                            <div className="font-medium text-sm">{log.action}</div>
                            <div className="flex items-center gap-2">
                              <Badge variant={
                                log.status === 'success' ? 'default' :
                                log.status === 'error' ? 'destructive' :
                                log.status === 'warning' ? 'secondary' : 'outline'
                              } className="text-xs">
                                {log.status === 'success' ? '成功' :
                                 log.status === 'error' ? '失败' :
                                 log.status === 'warning' ? '警告' : '处理中'}
                              </Badge>
                              <span className="text-xs text-gray-400">
                                {new Date(log.created_at).toLocaleString()}
                              </span>
                            </div>
                          </div>
                          <div className="text-xs text-gray-600">
                            类型: {log.operation_type} | 目标: {log.target_type || '系统'}
                          </div>
                          {log.error_message && (
                            <div className="text-xs text-red-600 mt-1">
                              错误: {log.error_message}
                            </div>
                          )}
                          {log.details && Object.keys(log.details).length > 0 && (
                            <div className="text-xs text-gray-500 mt-1">
                              详情: {JSON.stringify(log.details).substring(0, 100)}...
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 数据统计标签页 */}
          <TabsContent value="statistics" className="space-y-4">
            {statistics ? (
              <>
                {/* 概览统计 */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Card>
                    <CardContent className="p-4">
                      <div className="text-2xl font-bold">{statistics.overview?.totalTools || 0}</div>
                      <div className="text-sm text-gray-600">总工具数</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="text-2xl font-bold text-green-600">
                        +{statistics.overview?.todayAdded || 0}
                      </div>
                      <div className="text-sm text-gray-600">今日新增</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="text-2xl font-bold text-blue-600">
                        {statistics.overview?.thisMonthAdded || 0}
                      </div>
                      <div className="text-sm text-gray-600">本月新增</div>
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="text-2xl font-bold text-purple-600">
                        {statistics.overview?.averagePerDay?.toFixed(1) || '0.0'}
                      </div>
                      <div className="text-sm text-gray-600">日均新增</div>
                    </CardContent>
                  </Card>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* 分类统计 */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <BarChart3 className="h-4 w-4" />
                        分类分布
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ScrollArea className="h-64">
                        <div className="space-y-2">
                          {Array.isArray(statistics.categories) ?
                            statistics.categories.slice(0, 10).map((category) => (
                              <div key={category.name} className="flex items-center justify-between">
                                <div className="text-sm">{category.name}</div>
                                <div className="flex items-center gap-2">
                                  <div className="text-sm font-medium">{category.count}</div>
                                  <div className="text-xs text-gray-500">
                                    {category.percentage}%
                                  </div>
                                </div>
                              </div>
                            )) : (
                              <div className="text-center text-gray-500 py-4">
                                暂无分类数据
                              </div>
                            )
                          }
                        </div>
                      </ScrollArea>
                    </CardContent>
                  </Card>

                  {/* 热门标签 */}
                  <Card>
                    <CardHeader>
                      <CardTitle>热门标签</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <ScrollArea className="h-64">
                        <div className="flex flex-wrap gap-2">
                          {Array.isArray(statistics.tags) ?
                            statistics.tags.slice(0, 30).map((tag) => (
                              <Badge key={tag.name} variant="outline" className="text-xs">
                                {tag.name} ({tag.count})
                              </Badge>
                            )) : (
                              <div className="text-center text-gray-500 py-4">
                                暂无标签数据
                              </div>
                            )
                          }
                        </div>
                      </ScrollArea>
                    </CardContent>
                  </Card>
                </div>

                {/* 质量统计 */}
                <Card>
                  <CardHeader>
                    <CardTitle>数据质量</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <div className="text-lg font-semibold text-green-600">
                          {statistics.quality?.withDescription || 0}
                        </div>
                        <div className="text-sm text-gray-600">有描述</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-semibold text-blue-600">
                          {statistics.quality?.withTags || 0}
                        </div>
                        <div className="text-sm text-gray-600">有标签</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-semibold text-orange-600">
                          {statistics.quality?.duplicateUrls || 0}
                        </div>
                        <div className="text-sm text-gray-600">重复URL</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-semibold text-red-600">
                          {statistics.quality?.invalidUrls || 0}
                        </div>
                        <div className="text-sm text-gray-600">无效URL</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </>
            ) : (
              <Card>
                <CardContent className="p-8 text-center">
                  <Loader2 className="mx-auto h-8 w-8 animate-spin mb-4" />
                  <div>正在计算统计数据...</div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>

    {/* 确认对话框 */}
    <AlertDialog open={confirmDialog.isOpen} onOpenChange={(open) =>
      setConfirmDialog(prev => ({ ...prev, isOpen: open }))
    }>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{confirmDialog.title}</AlertDialogTitle>
          <AlertDialogDescription>
            {confirmDialog.description}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>取消</AlertDialogCancel>
          <AlertDialogAction
            onClick={confirmDialog.onConfirm}
            className={confirmDialog.variant === 'destructive' ? 'bg-red-600 hover:bg-red-700' : ''}
          >
            {confirmDialog.confirmText}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </>
  )
}
