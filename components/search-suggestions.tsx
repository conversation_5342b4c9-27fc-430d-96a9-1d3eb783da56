"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import type { Tool } from "@/components/toolbox"

interface SearchSuggestionsProps {
  searchQuery: string
  tools: Tool[]
  onSuggestionClick: (suggestion: string) => void
}

export function SearchSuggestions({ searchQuery, tools, onSuggestionClick }: SearchSuggestionsProps) {
  const lowerCaseQuery = searchQuery.toLowerCase()

  // Get unique tags that match the query
  const matchingTags = Array.from(
    new Set(
      tools.flatMap((tool) =>
        tool.tags.filter((tag) => tag.toLowerCase().includes(lowerCaseQuery)).map((tag) => tag.toLowerCase()),
      ),
    ),
  ).slice(0, 5) // Limit to 5 tag suggestions

  // Get unique tool names that match the query
  const matchingToolNames = Array.from(
    new Set(
      tools.filter((tool) => tool.name.toLowerCase().includes(lowerCaseQuery)).map((tool) => tool.name.toLowerCase()),
    ),
  ).slice(0, 5) // Limit to 5 name suggestions

  // Combine and deduplicate suggestions
  const suggestions = Array.from(new Set([...matchingToolNames, ...matchingTags])).slice(0, 10) // Overall limit

  if (suggestions.length === 0) {
    return null
  }

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="text-lg">搜索建议</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-wrap gap-2">
        {suggestions.map((suggestion, index) => (
          <Button key={index} variant="outline" onClick={() => onSuggestionClick(suggestion)}>
            {suggestion}
          </Button>
        ))}
      </CardContent>
    </Card>
  )
}
