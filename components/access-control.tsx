"use client"

import React, { useState, useEffect } from "react"
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Lock, AlertTriangle } from "lucide-react"
import { SECURITY_CONFIG } from "@/lib/config"
import { setAuthStatus } from "@/lib/auth-utils"
import { setApiKey } from "@/lib/api-auth"

interface AccessControlProps {
  onAuthenticated: () => void
}

export function AccessControl({ onAuthenticated }: AccessControlProps) {
  const [password, setPassword] = useState("")
  const [error, setError] = useState<string | null>(null)
  const [isLocked, setIsLocked] = useState(false)
  const [lockoutTimeRemaining, setLockoutTimeRemaining] = useState(0)
  const [failedAttempts, setFailedAttempts] = useState(0)
  const [isLoading, setIsLoading] = useState(false)

  // 检查是否被锁定
  useEffect(() => {
    checkLockoutStatus()
    const interval = setInterval(checkLockoutStatus, 1000)
    return () => clearInterval(interval)
  }, [])

  const checkLockoutStatus = () => {
    const lockoutUntil = localStorage.getItem(SECURITY_CONFIG.STORAGE_KEYS.LOCKOUT_UNTIL)
    const attempts = localStorage.getItem(SECURITY_CONFIG.STORAGE_KEYS.FAILED_ATTEMPTS)

    if (lockoutUntil) {
      const lockoutTime = parseInt(lockoutUntil)
      const now = Date.now()

      if (now < lockoutTime) {
        setIsLocked(true)
        setLockoutTimeRemaining(Math.ceil((lockoutTime - now) / 1000))
      } else {
        // 锁定时间已过，清除锁定状态
        setIsLocked(false)
        setLockoutTimeRemaining(0)
        localStorage.removeItem(SECURITY_CONFIG.STORAGE_KEYS.LOCKOUT_UNTIL)
        localStorage.removeItem(SECURITY_CONFIG.STORAGE_KEYS.FAILED_ATTEMPTS)
        setFailedAttempts(0)
      }
    }

    if (attempts) {
      setFailedAttempts(parseInt(attempts))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)
    setIsLoading(true)

    if (isLocked) {
      setError(`访问被锁定，请等待 ${Math.ceil(lockoutTimeRemaining / 60)} 分钟后重试`)
      setIsLoading(false)
      return
    }

    if (!password.trim()) {
      setError("请输入访问密码")
      setIsLoading(false)
      return
    }

    // 直接发送到后端验证密码
    try {
      // 获取API访问密钥
      const response = await fetch('/api/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ password: password.trim() })
      })

      if (response.ok) {
        const data = await response.json()

        // 存储API密钥
        setApiKey(data.apiKey)

        // 密码正确，清除失败记录
        localStorage.removeItem(SECURITY_CONFIG.STORAGE_KEYS.FAILED_ATTEMPTS)
        localStorage.removeItem(SECURITY_CONFIG.STORAGE_KEYS.LOCKOUT_UNTIL)
        localStorage.removeItem(SECURITY_CONFIG.STORAGE_KEYS.LOCKOUT_COUNT)

        // 设置认证状态和时间戳
        setAuthStatus()

        onAuthenticated()
      } else {
        // 后端验证失败，按照原来的逻辑处理失败次数
        const newFailedAttempts = failedAttempts + 1
        setFailedAttempts(newFailedAttempts)
        localStorage.setItem(SECURITY_CONFIG.STORAGE_KEYS.FAILED_ATTEMPTS, newFailedAttempts.toString())

        if (newFailedAttempts >= SECURITY_CONFIG.MAX_ATTEMPTS_PER_WINDOW) {
          // 达到最大尝试次数，开始锁定
          const lockoutCount = parseInt(localStorage.getItem(SECURITY_CONFIG.STORAGE_KEYS.LOCKOUT_COUNT) || "0")
          const lockoutDuration = SECURITY_CONFIG.LOCKOUT_DURATIONS[Math.min(lockoutCount, SECURITY_CONFIG.LOCKOUT_DURATIONS.length - 1)]
          const lockoutUntil = Date.now() + lockoutDuration

          localStorage.setItem(SECURITY_CONFIG.STORAGE_KEYS.LOCKOUT_UNTIL, lockoutUntil.toString())
          localStorage.setItem(SECURITY_CONFIG.STORAGE_KEYS.LOCKOUT_COUNT, (lockoutCount + 1).toString())
          localStorage.removeItem(SECURITY_CONFIG.STORAGE_KEYS.FAILED_ATTEMPTS)

          setIsLocked(true)
          setLockoutTimeRemaining(Math.ceil(lockoutDuration / 1000))
          setFailedAttempts(0)
          setError(`密码错误次数过多，访问已被锁定 ${Math.ceil(lockoutDuration / 60000)} 分钟`)
        } else {
          const remainingAttempts = SECURITY_CONFIG.MAX_ATTEMPTS_PER_WINDOW - newFailedAttempts
          setError(`访问密码错误，还有 ${remainingAttempts} 次尝试机会`)
        }
      }
    } catch (error) {
      console.error('认证请求失败:', error)
      setError('网络错误，请重试')
    }

    setPassword("")
    setIsLoading(false)
  }

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <Lock className="h-12 w-12 text-primary" />
          </div>
          <CardTitle className="text-2xl">ToolMaster</CardTitle>
          <CardDescription>
            请输入访问密码以继续使用
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="password">访问密码</Label>
              <Input
                id="password"
                type="password"
                placeholder="请输入访问密码"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                disabled={isLocked || isLoading}
                className="text-center"
              />
            </div>

            {error && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {isLocked && lockoutTimeRemaining > 0 && (
              <Alert>
                <Lock className="h-4 w-4" />
                <AlertDescription>
                  访问被锁定，剩余时间: {formatTime(lockoutTimeRemaining)}
                </AlertDescription>
              </Alert>
            )}

            {failedAttempts > 0 && !isLocked && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  已失败 {failedAttempts} 次，还有 {SECURITY_CONFIG.MAX_ATTEMPTS_PER_WINDOW - failedAttempts} 次机会
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
          <CardFooter>
            <Button
              type="submit"
              className="w-full"
              disabled={isLocked || isLoading}
            >
              {isLoading ? "验证中..." : isLocked ? "已锁定" : "进入"}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  )
}
