import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  // 获取请求的路径
  const { pathname } = request.nextUrl

  // 允许访问的公共路径（API路由等）
  const publicPaths = [
    '/api/',
    '/_next/',
    '/favicon.ico',
    '/placeholder',
  ]

  // 检查是否是公共路径
  const isPublicPath = publicPaths.some(path => pathname.startsWith(path))
  
  if (isPublicPath) {
    return NextResponse.next()
  }

  // 对于所有其他路径，让客户端组件处理认证
  // 这里我们不做服务端认证检查，因为认证状态存储在localStorage中
  // 认证检查将在客户端组件中进行
  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
