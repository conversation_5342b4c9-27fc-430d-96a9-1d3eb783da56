<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全网搜索功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-button {
            background: linear-gradient(to right, #10b981, #0d9488);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .test-button:hover {
            opacity: 0.9;
        }
        .test-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
        }
        .error {
            background: #fee;
            color: #c33;
        }
        .success {
            background: #efe;
            color: #363;
        }
        .tool-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .tool-name {
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        .tool-url {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 10px;
        }
        .tool-desc {
            margin-bottom: 10px;
        }
        .tool-tags {
            font-size: 0.8em;
            color: #28a745;
            margin-bottom: 5px;
        }
        .tool-meta {
            font-size: 0.8em;
            color: #666;
        }
    </style>
</head>
<body>
    <h1>ToolMaster 全网搜索功能测试</h1>

    <div class="test-section">
        <h2>修复验证</h2>
        <p><strong>已修复的问题：</strong></p>
        <ul>
            <li>✅ 修复了API返回数组格式但前端期望对象格式的问题</li>
            <li>✅ 添加了数据格式转换逻辑</li>
            <li>✅ 增强了错误处理和调试信息</li>
            <li>✅ 修复了过滤逻辑中的安全检查</li>
        </ul>

        <h3>功能特点：</h3>
        <ul>
            <li>✅ 在全网范围内搜索最优秀的工具</li>
            <li>✅ 返回标准的工具数据格式（名称、描述、标签、URL等）</li>
            <li>✅ 结果保存在缓存中，不直接入库</li>
            <li>✅ 工具卡片有"添加到本站"功能</li>
            <li>✅ 与深度搜索结果展示格式一致</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>全网搜索测试</h2>
        <input type="text" id="globalSearchQuery" class="test-input"
               placeholder="输入全网搜索查询，例如：macos最好用的压缩软件"
               value="macos最好用的压缩软件">
        <br>
        <button id="testGlobalSearch" class="test-button">执行全网搜索</button>
        <button id="clearResults" class="test-button" style="background: #6c757d;">清除结果</button>
        <div id="globalSearchResult" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>建议测试用例</h2>
        <p>点击下方按钮快速测试：</p>
        <button class="test-button" onclick="testQuery('macos最好用的压缩软件')">macOS压缩软件</button>
        <button class="test-button" onclick="testQuery('最强大的pdf编辑工具')">PDF编辑工具</button>
        <button class="test-button" onclick="testQuery('最受欢迎的代码编辑器')">代码编辑器</button>
        <button class="test-button" onclick="testQuery('最好用的思维导图软件')">思维导图软件</button>
        <button class="test-button" onclick="testQuery('最实用的截图工具')">截图工具</button>
    </div>

    <script>
        function testQuery(query) {
            document.getElementById('globalSearchQuery').value = query;
            document.getElementById('testGlobalSearch').click();
        }

        document.getElementById('testGlobalSearch').addEventListener('click', async () => {
            const query = document.getElementById('globalSearchQuery').value;
            const button = document.getElementById('testGlobalSearch');
            const resultDiv = document.getElementById('globalSearchResult');

            if (!query.trim()) {
                alert('请输入搜索查询');
                return;
            }

            button.disabled = true;
            button.textContent = '全网搜索中...';
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '正在执行全网搜索...';

            try {
                const response = await fetch('http://localhost:3001/api/global-search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: query
                    })
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    resultDiv.className = 'result success';

                    // 格式化显示结果
                    let displayHTML = `<h3>✅ 全网搜索成功！</h3>`;

                    if (data.data && data.data.recommendedTools) {
                        displayHTML += `<p><strong>📊 搜索理解:</strong> ${data.data.searchSummary || '无'}</p>`;
                        displayHTML += `<p><strong>💡 搜索洞察:</strong> ${data.data.searchInsights || '无'}</p>`;
                        displayHTML += `<h4>🎯 推荐工具 (${data.data.recommendedTools.length}个):</h4>`;

                        data.data.recommendedTools.forEach((tool, index) => {
                            displayHTML += `
                                <div class="tool-card">
                                    <div class="tool-name">${index + 1}. ${tool.name}</div>
                                    <div class="tool-url">🔗 ${tool.url}</div>
                                    <div class="tool-desc">${tool.description}</div>
                                    <div class="tool-tags">🏷️ 标签: ${tool.tags.join(', ')}</div>
                                    <div class="tool-meta">
                                        📂 分类: ${tool.category} > ${tool.subcategory} > ${tool.subsubcategory} |
                                        ⭐ 相关性: ${Math.round(tool.relevanceScore * 100)}% |
                                        💡 推荐理由: ${tool.recommendReason}
                                    </div>
                                </div>
                            `;
                        });
                    } else {
                        displayHTML += `<p>未找到推荐工具</p>`;
                        displayHTML += `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                    }

                    resultDiv.innerHTML = displayHTML;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `错误: ${data.error || '未知错误'}\n详情: ${data.details || '无'}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `请求失败: ${error.message}`;
            } finally {
                button.disabled = false;
                button.textContent = '执行全网搜索';
            }
        });

        document.getElementById('clearResults').addEventListener('click', () => {
            const resultDiv = document.getElementById('globalSearchResult');
            resultDiv.style.display = 'none';
            resultDiv.innerHTML = '';
        });
    </script>
</body>
</html>
