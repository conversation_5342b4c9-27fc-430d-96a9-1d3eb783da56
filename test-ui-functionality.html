<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ToolMaster 功能测试清单</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.pending { background: #ffc107; color: #000; }
        .status.testing { background: #17a2b8; color: white; }
        .status.passed { background: #28a745; color: white; }
        .status.failed { background: #dc3545; color: white; }
        .url {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;
        }
        .url:hover {
            text-decoration: underline;
        }
        .instructions {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <h1>🧪 ToolMaster 增强数据管理功能测试清单</h1>

    <div class="instructions">
        <h3>📋 测试说明</h3>
        <p>请按照以下清单逐项测试新增的数据管理功能。每完成一项测试，请在浏览器控制台记录结果。</p>
        <p><strong>测试地址：</strong> <a href="http://localhost:3001" class="url" target="_blank">http://localhost:3001</a></p>
    </div>

    <div class="test-section">
        <h2>🌐 基础功能测试</h2>

        <div class="test-item">
            <span class="status pending">待测试</span>
            <strong>页面加载</strong>
            <p>访问主页，确认页面正常加载，无控制台错误</p>
        </div>

        <div class="test-item">
            <span class="status pending">待测试</span>
            <strong>数据管理按钮</strong>
            <p>点击工具栏中的"数据管理"按钮，确认弹窗正常打开</p>
        </div>

        <div class="test-item">
            <span class="status pending">待测试</span>
            <strong>标签页切换</strong>
            <p>测试5个标签页（批量导入、数据导出、数据清理、操作日志、数据统计）是否能正常切换</p>
        </div>
    </div>

    <div class="test-section">
        <h2>📥 批量导入功能测试</h2>

        <div class="test-item">
            <span class="status pending">待测试</span>
            <strong>文本导入</strong>
            <p>在文本框中输入以下内容并点击"开始导入"：</p>
            <pre>https://github.com
https://www.figma.com
https://notion.so</pre>
        </div>

        <div class="test-item">
            <span class="status pending">待测试</span>
            <strong>导入任务状态</strong>
            <p>确认导入任务出现在"导入任务历史"中，状态从"处理中"变为"已完成"</p>
        </div>

        <div class="test-item">
            <span class="status pending">待测试</span>
            <strong>文件导入</strong>
            <p>创建一个包含URL的txt文件，测试文件上传导入功能</p>
        </div>
    </div>

    <div class="test-section">
        <h2>📤 数据导出功能测试</h2>

        <div class="test-item">
            <span class="status pending">待测试</span>
            <strong>数据导出</strong>
            <p>点击"下载数据文件"按钮，确认浏览器自动下载JSON文件</p>
        </div>

        <div class="test-item">
            <span class="status pending">待测试</span>
            <strong>导出文件验证</strong>
            <p>打开下载的JSON文件，确认包含tools和categories数据</p>
        </div>
    </div>

    <div class="test-section">
        <h2>🧹 数据清理功能测试</h2>

        <div class="test-item">
            <span class="status pending">待测试</span>
            <strong>移除重复工具</strong>
            <p>点击"移除重复工具"按钮，查看清理结果</p>
        </div>

        <div class="test-item">
            <span class="status pending">待测试</span>
            <strong>删除无效工具</strong>
            <p>点击"删除无效工具"按钮，查看清理结果</p>
        </div>

        <div class="test-item">
            <span class="status pending">待测试</span>
            <strong>合并重复信息</strong>
            <p>点击"合并重复信息"按钮，查看AI合并结果</p>
        </div>

        <div class="test-item">
            <span class="status pending">待测试</span>
            <strong>清理报告</strong>
            <p>确认清理操作后，右侧"清理报告"区域显示详细结果</p>
        </div>
    </div>

    <div class="test-section">
        <h2>📋 操作日志功能测试</h2>

        <div class="test-item">
            <span class="status pending">待测试</span>
            <strong>日志记录</strong>
            <p>执行任意操作后，在"操作日志"标签页查看是否有相应记录</p>
        </div>

        <div class="test-item">
            <span class="status pending">待测试</span>
            <strong>日志详情</strong>
            <p>确认日志显示操作时间、类型、状态等信息</p>
        </div>

        <div class="test-item">
            <span class="status pending">待测试</span>
            <strong>清空日志</strong>
            <p>点击"清空日志"按钮，确认日志被清空</p>
        </div>
    </div>

    <div class="test-section">
        <h2>📊 数据统计功能测试</h2>

        <div class="test-item">
            <span class="status pending">待测试</span>
            <strong>概览统计</strong>
            <p>查看总工具数、今日新增、本月新增、日均新增等数据</p>
        </div>

        <div class="test-item">
            <span class="status pending">待测试</span>
            <strong>分类分布</strong>
            <p>查看左侧"分类分布"图表，确认数据正确</p>
        </div>

        <div class="test-item">
            <span class="status pending">待测试</span>
            <strong>热门标签</strong>
            <p>查看右侧"热门标签"列表，确认标签统计正确</p>
        </div>

        <div class="test-item">
            <span class="status pending">待测试</span>
            <strong>数据质量</strong>
            <p>查看底部"数据质量"统计，包括有描述、有标签、重复URL、无效URL等</p>
        </div>
    </div>

    <div class="test-section">
        <h2>🤖 AI分析功能测试</h2>

        <div class="test-item">
            <span class="status pending">待测试</span>
            <strong>快速添加AI分析</strong>
            <p>使用快速添加功能，输入URL：https://v.qq.com/channel/cartoon，点击"分析"</p>
        </div>

        <div class="test-item">
            <span class="status pending">待测试</span>
            <strong>AI分析结果</strong>
            <p>确认AI正确识别为"腾讯视频"，并自动填充描述、标签、分类</p>
        </div>

        <div class="test-item">
            <span class="status pending">待测试</span>
            <strong>批量AI分析</strong>
            <p>在批量导入中测试AI是否能正确分析多个URL</p>
        </div>
    </div>

    <div class="test-section">
        <h2>🔄 实时同步测试</h2>

        <div class="test-item">
            <span class="status pending">待测试</span>
            <strong>多标签页同步</strong>
            <p>打开两个浏览器标签页，在一个页面添加工具，查看另一个页面是否实时更新</p>
        </div>

        <div class="test-item">
            <span class="status pending">待测试</span>
            <strong>同步状态指示器</strong>
            <p>查看右上角同步状态指示器是否正常显示</p>
        </div>
    </div>

    <div class="instructions">
        <h3>✅ 测试完成后</h3>
        <p>如果所有功能都正常工作，说明增强数据管理功能集成成功！</p>
        <p>如果发现任何问题，请记录具体的错误信息和复现步骤。</p>
    </div>

    <script>
        // 简单的测试状态管理
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('status')) {
                const statuses = ['pending', 'testing', 'passed', 'failed'];
                const current = statuses.find(s => e.target.classList.contains(s));
                const currentIndex = statuses.indexOf(current);
                const nextIndex = (currentIndex + 1) % statuses.length;

                e.target.classList.remove(current);
                e.target.classList.add(statuses[nextIndex]);
                e.target.textContent = {
                    'pending': '待测试',
                    'testing': '测试中',
                    'passed': '通过',
                    'failed': '失败'
                }[statuses[nextIndex]];
            }
        });
    </script>
</body>
</html>
