#!/usr/bin/env node

/**
 * ToolMaster 命名一致性验证脚本
 * 用于验证所有 "toolshift" 到 "toolmaster" 的修改是否正确完成
 */

const fs = require('fs');
const path = require('path');

// 需要检查的文件列表
const filesToCheck = [
  'app/page.tsx',
  'app/api/fetch-content/route.ts',
  'components/enhanced-data-management.tsx',
  'lib/config.ts',
  'lib/database.ts',
  'lib/hybrid-storage.ts',
  'package.json',
  'README.md'
];

// 应该使用 "toolmaster" 的模式
const correctPatterns = [
  /toolmaster-tools/g,
  /toolmaster-categories/g,
  /toolmaster-logs/g,
  /toolmaster-search-history/g,
  /toolmaster-sync-queue/g,
  /toolmaster-export-/g,
  /toolmaster-category-/g,
  /ToolMaster_Category_Export/g,
  /exportedBy: "ToolMaster"/g,
  /ToolMasterApp/g,
  /ToolMaster\/1\.0/g
];

// 不应该存在的旧模式（除了迁移代码和注释中的引用）
const oldPatterns = [
  // 检查是否有未修改的 toolshift- 前缀（排除迁移代码中的合理引用）
  {
    pattern: /toolshift-/g,
    allowedContexts: [
      'localStorage.getItem("toolshift-logs")',
      'localStorage.removeItem("toolshift-logs")',
      '// 数据迁移：将旧的 toolshift-logs 迁移到 toolmaster-logs'
    ]
  },
  // 检查是否有未修改的 ToolShift 组件名或标识符
  {
    pattern: /ToolShift(?!\/1\.0)/g,
    allowedContexts: []
  }
];

console.log('🔍 开始验证 ToolMaster 命名一致性...\n');

let hasErrors = false;
let totalChecks = 0;
let passedChecks = 0;

// 检查单个文件
function checkFile(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  文件不存在: ${filePath}`);
    return;
  }

  const content = fs.readFileSync(filePath, 'utf8');
  const fileName = path.basename(filePath);
  
  console.log(`📄 检查文件: ${fileName}`);
  
  let fileHasErrors = false;
  
  // 检查是否包含正确的模式
  correctPatterns.forEach(pattern => {
    const matches = content.match(pattern);
    if (matches) {
      console.log(`  ✅ 找到正确模式: ${pattern.source} (${matches.length} 处)`);
      passedChecks++;
    }
    totalChecks++;
  });
  
  // 检查是否还有旧的模式
  oldPatterns.forEach(patternObj => {
    const matches = content.match(patternObj.pattern);
    if (matches) {
      // 检查是否在允许的上下文中
      const lines = content.split('\n');
      const problematicMatches = [];

      matches.forEach(match => {
        lines.forEach((line, index) => {
          if (line.includes(match)) {
            const isAllowed = patternObj.allowedContexts.some(context =>
              line.includes(context)
            );
            if (!isAllowed) {
              problematicMatches.push({ match, line: line.trim(), lineNumber: index + 1 });
            }
          }
        });
      });

      if (problematicMatches.length > 0) {
        console.log(`  ❌ 发现旧模式: ${patternObj.pattern.source} (${problematicMatches.length} 处)`);
        problematicMatches.forEach(item => {
          console.log(`     第 ${item.lineNumber} 行: ${item.line}`);
        });
        fileHasErrors = true;
        hasErrors = true;
      } else {
        console.log(`  ✅ 旧模式在允许的上下文中: ${patternObj.pattern.source}`);
        passedChecks++;
      }
    } else {
      passedChecks++;
    }
    totalChecks++;
  });
  
  if (!fileHasErrors) {
    console.log(`  ✅ ${fileName} 检查通过`);
  }
  
  console.log('');
}

// 检查特定的修改点
function checkSpecificChanges() {
  console.log('🎯 检查特定修改点...\n');
  
  // 检查组件名称
  const pageContent = fs.readFileSync('app/page.tsx', 'utf8');
  if (pageContent.includes('export default function ToolMasterApp()')) {
    console.log('✅ 组件名称已正确修改为 ToolMasterApp');
    passedChecks++;
  } else {
    console.log('❌ 组件名称修改失败');
    hasErrors = true;
  }
  totalChecks++;
  
  // 检查 User-Agent
  const fetchContent = fs.readFileSync('app/api/fetch-content/route.ts', 'utf8');
  if (fetchContent.includes('ToolMaster/1.0')) {
    console.log('✅ User-Agent 已正确修改');
    passedChecks++;
  } else {
    console.log('❌ User-Agent 修改失败');
    hasErrors = true;
  }
  totalChecks++;
  
  // 检查导出元数据
  if (pageContent.includes('ToolMaster_Category_Export') && pageContent.includes('exportedBy: "ToolMaster"')) {
    console.log('✅ 导出元数据已正确修改');
    passedChecks++;
  } else {
    console.log('❌ 导出元数据修改失败');
    hasErrors = true;
  }
  totalChecks++;
  
  // 检查数据迁移逻辑
  if (pageContent.includes('toolshift-logs') && pageContent.includes('toolmaster-logs')) {
    console.log('✅ 数据迁移逻辑已添加');
    passedChecks++;
  } else {
    console.log('❌ 数据迁移逻辑缺失');
    hasErrors = true;
  }
  totalChecks++;
  
  console.log('');
}

// 检查 package.json
function checkPackageJson() {
  console.log('📦 检查 package.json...\n');
  
  const packageContent = fs.readFileSync('package.json', 'utf8');
  const packageData = JSON.parse(packageContent);
  
  if (packageData.name === 'toolmaster') {
    console.log('✅ package.json 项目名称正确');
    passedChecks++;
  } else {
    console.log('❌ package.json 项目名称不正确');
    hasErrors = true;
  }
  totalChecks++;
  
  console.log('');
}

// 生成测试报告
function generateReport() {
  console.log('📊 验证报告');
  console.log('='.repeat(50));
  console.log(`总检查项: ${totalChecks}`);
  console.log(`通过检查: ${passedChecks}`);
  console.log(`失败检查: ${totalChecks - passedChecks}`);
  console.log(`成功率: ${Math.round((passedChecks / totalChecks) * 100)}%`);
  
  if (hasErrors) {
    console.log('\n❌ 验证失败！发现问题需要修复。');
    process.exit(1);
  } else {
    console.log('\n🎉 验证成功！所有命名修改都已正确完成。');
    console.log('\n📋 后续建议:');
    console.log('1. 运行 npm run dev 测试应用是否正常启动');
    console.log('2. 测试导出功能，确认文件名正确');
    console.log('3. 检查浏览器开发者工具中的网络请求 User-Agent');
    console.log('4. 验证日志数据迁移功能');
    process.exit(0);
  }
}

// 主执行流程
console.log('ToolMaster 命名一致性验证脚本');
console.log('='.repeat(50));

// 检查所有文件
filesToCheck.forEach(checkFile);

// 检查特定修改点
checkSpecificChanges();

// 检查 package.json
checkPackageJson();

// 生成报告
generateReport();
