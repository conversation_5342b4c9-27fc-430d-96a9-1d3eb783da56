# Toast 提示测试指南

## 问题描述

您反馈批量导入时没有看到以下提示：
1. 点击"开始导入"后的"导入中，请稍等..."提示
2. 导入完成后的"文本导入已完成"提示

## 修复内容

我已经对代码进行了以下修复：

### 1. 增强了导入开始提示
- 增加了console.log调试信息
- 延长了toast显示时间到10秒
- 确保toast在点击按钮后立即显示

### 2. 改进了任务监控逻辑
- 添加了详细的console.log调试信息
- 缩短了监控间隔（从3秒改为1秒开始）
- 改进了错误处理逻辑

### 3. 优化了完成提示
- 统一了完成提示的显示方式
- 延长了完成提示的显示时间到5秒
- 添加了更多调试信息

## 测试步骤

### 步骤1：检查基本toast功能
1. 启动项目：`npm run dev`
2. 打开浏览器开发者工具（F12）
3. 在控制台中输入以下代码测试toast是否正常：

```javascript
// 测试基本toast功能
document.dispatchEvent(new CustomEvent('toast', {
  detail: {
    title: '测试提示',
    description: '这是一个测试提示',
    duration: 5000
  }
}))
```

### 步骤2：测试批量导入提示
1. 打开"数据管理"对话框
2. 切换到"批量导入"标签
3. 在文本框中输入一些测试内容，例如：
```
GitHub - https://github.com
Google - https://google.com
```
4. 点击"开始导入"按钮
5. 观察以下内容：
   - 立即出现"导入中，请稍等..."提示
   - 控制台显示调试信息
   - 按钮显示loading状态

### 步骤3：检查控制台日志
在执行导入时，控制台应该显示以下日志：
```
显示导入中提示...
开始文本导入...
导入任务已创建，ID: [任务ID]
开始监控导入任务: [任务ID], 类型: text
任务状态检查: [任务ID] [任务对象]
```

### 步骤4：等待完成提示
- 等待几秒钟，应该看到"文本导入已完成"的提示
- 控制台会显示完成相关的日志

## 可能的问题和解决方案

### 问题1：toast组件未正确配置
**症状**：没有任何提示显示
**解决**：检查app/layout.tsx中是否包含了Toaster组件

### 问题2：Supabase连接问题
**症状**：导入任务创建失败
**解决**：检查.env.local文件中的Supabase配置

### 问题3：AI服务问题
**症状**：任务创建成功但处理失败
**解决**：检查AI服务的API密钥配置

## 调试命令

如果仍然有问题，可以在浏览器控制台中使用以下命令进行调试：

```javascript
// 检查toast函数是否可用
console.log(typeof toast)

// 手动触发toast
if (typeof toast === 'function') {
  toast({
    title: '手动测试',
    description: '这是手动触发的toast',
    duration: 5000
  })
}

// 检查导入任务状态
// 需要先获取任务ID，然后：
// BatchImportService.getImportTaskStatus('任务ID').then(console.log)
```

## 预期结果

修复后，您应该能看到：
1. ✅ 点击导入按钮后立即显示"导入中，请稍等..."
2. ✅ 任务创建成功后显示"导入任务已创建"
3. ✅ 导入完成后显示"文本导入已完成"
4. ✅ 控制台显示详细的调试信息

如果仍然有问题，请查看控制台的错误信息并反馈给我。
