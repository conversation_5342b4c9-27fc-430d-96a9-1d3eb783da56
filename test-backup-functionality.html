<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ToolMaster 备份功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f8fafc;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        .test-section {
            background: white;
            padding: 25px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .test-button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .success-button {
            background: #059669;
        }
        .success-button:hover {
            background: #047857;
        }
        .danger-button {
            background: #dc2626;
        }
        .danger-button:hover {
            background: #b91c1c;
        }
        .test-results {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .status.success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }
        .status.error {
            background: #fef2f2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }
        .status.warning {
            background: #fefce8;
            color: #a16207;
            border: 1px solid #fde68a;
        }
        .config-info {
            background: #f1f5f9;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔄 ToolMaster 腾讯云COS备份功能测试</h1>
        <p>测试自动备份功能是否正常工作</p>
    </div>

    <div class="test-section">
        <h2>📋 配置信息</h2>
        <div class="config-info">
            <p><strong>存储桶</strong>: toolmaster-250730-1308117310</p>
            <p><strong>地域</strong>: ap-guangzhou</p>
            <p><strong>备份路径</strong>: backups/toolmaster</p>
            <p><strong>API端点</strong>: /api/backup</p>
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 连接测试</h2>
        <button class="test-button" onclick="testCOSConnection()">测试COS连接</button>
        <div id="connectionResults" class="test-results" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>💾 备份测试</h2>
        <button class="test-button success-button" onclick="performManualBackup()">执行手动备份</button>
        <button class="test-button" onclick="getBackupHistory()">获取备份历史</button>
        <div id="backupResults" class="test-results" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>⏰ 定时任务测试</h2>
        <p>定时任务配置：每天凌晨4点执行自动备份</p>
        <button class="test-button" onclick="testCronEndpoint()">测试定时任务端点</button>
        <div id="cronResults" class="test-results" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h2>📊 测试状态</h2>
        <div id="testStatus">
            <div class="status warning">
                <strong>等待测试</strong> - 点击上方按钮开始测试
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3000/api/backup'
        const API_KEY = 'Ln9boY934WtQEHXNcXJQhiV6ZLBahpfR' // 从环境变量获取

        // 显示加载状态
        function showLoading(buttonElement) {
            buttonElement.disabled = true
            buttonElement.innerHTML = '<span class="loading"></span> 执行中...'
        }

        // 恢复按钮状态
        function restoreButton(buttonElement, originalText) {
            buttonElement.disabled = false
            buttonElement.innerHTML = originalText
        }

        // 更新测试状态
        function updateTestStatus(message, type = 'warning') {
            const statusDiv = document.getElementById('testStatus')
            statusDiv.innerHTML = `
                <div class="status ${type}">
                    <strong>${type === 'success' ? '✅ 成功' : type === 'error' ? '❌ 失败' : '⏳ 进行中'}</strong> - ${message}
                </div>
            `
        }

        // 测试COS连接
        async function testCOSConnection() {
            const button = event.target
            const originalText = button.innerHTML
            showLoading(button)
            updateTestStatus('正在测试COS连接...')

            try {
                const response = await fetch(`${API_BASE}?action=test`, {
                    method: 'GET',
                    headers: {
                        'x-api-key': API_KEY,
                        'Content-Type': 'application/json'
                    }
                })

                const result = await response.json()

                const resultsDiv = document.getElementById('connectionResults')
                resultsDiv.style.display = 'block'
                resultsDiv.textContent = JSON.stringify(result, null, 2)

                if (result.success) {
                    updateTestStatus('COS连接测试成功', 'success')
                } else {
                    updateTestStatus(`COS连接测试失败: ${result.message}`, 'error')
                }

            } catch (error) {
                const resultsDiv = document.getElementById('connectionResults')
                resultsDiv.style.display = 'block'
                resultsDiv.textContent = `请求失败: ${error.message}`
                updateTestStatus(`COS连接测试异常: ${error.message}`, 'error')
            } finally {
                restoreButton(button, originalText)
            }
        }

        // 执行手动备份
        async function performManualBackup() {
            const button = event.target
            const originalText = button.innerHTML
            showLoading(button)
            updateTestStatus('正在执行手动备份...')

            try {
                const response = await fetch(API_BASE, {
                    method: 'POST',
                    headers: {
                        'x-api-key': API_KEY,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ action: 'manual' })
                })

                const result = await response.json()

                const resultsDiv = document.getElementById('backupResults')
                resultsDiv.style.display = 'block'
                resultsDiv.textContent = JSON.stringify(result, null, 2)

                if (result.success) {
                    updateTestStatus('手动备份执行成功', 'success')
                } else {
                    updateTestStatus(`手动备份执行失败: ${result.message}`, 'error')
                }

            } catch (error) {
                const resultsDiv = document.getElementById('backupResults')
                resultsDiv.style.display = 'block'
                resultsDiv.textContent = `请求失败: ${error.message}`
                updateTestStatus(`手动备份异常: ${error.message}`, 'error')
            } finally {
                restoreButton(button, originalText)
            }
        }

        // 获取备份历史
        async function getBackupHistory() {
            const button = event.target
            const originalText = button.innerHTML
            showLoading(button)
            updateTestStatus('正在获取备份历史...')

            try {
                const response = await fetch(`${API_BASE}?action=history&limit=5`, {
                    method: 'GET',
                    headers: {
                        'x-api-key': API_KEY,
                        'Content-Type': 'application/json'
                    }
                })

                const result = await response.json()

                const resultsDiv = document.getElementById('backupResults')
                resultsDiv.style.display = 'block'
                resultsDiv.textContent = JSON.stringify(result, null, 2)

                if (result.success) {
                    updateTestStatus(`成功获取备份历史，共 ${result.backups?.length || 0} 个备份`, 'success')
                } else {
                    updateTestStatus(`获取备份历史失败: ${result.message}`, 'error')
                }

            } catch (error) {
                const resultsDiv = document.getElementById('backupResults')
                resultsDiv.style.display = 'block'
                resultsDiv.textContent = `请求失败: ${error.message}`
                updateTestStatus(`获取备份历史异常: ${error.message}`, 'error')
            } finally {
                restoreButton(button, originalText)
            }
        }

        // 测试定时任务端点
        async function testCronEndpoint() {
            const button = event.target
            const originalText = button.innerHTML
            showLoading(button)
            updateTestStatus('正在测试定时任务端点...')

            try {
                const response = await fetch(API_BASE, {
                    method: 'GET',
                    headers: {
                        'x-api-key': API_KEY,
                        'Content-Type': 'application/json'
                    }
                })

                const result = await response.json()

                const resultsDiv = document.getElementById('cronResults')
                resultsDiv.style.display = 'block'
                resultsDiv.textContent = JSON.stringify(result, null, 2)

                if (result.success) {
                    updateTestStatus('定时任务端点测试成功', 'success')
                } else {
                    updateTestStatus(`定时任务端点测试失败: ${result.message}`, 'error')
                }

            } catch (error) {
                const resultsDiv = document.getElementById('cronResults')
                resultsDiv.style.display = 'block'
                resultsDiv.textContent = `请求失败: ${error.message}`
                updateTestStatus(`定时任务端点测试异常: ${error.message}`, 'error')
            } finally {
                restoreButton(button, originalText)
            }
        }

        // 页面加载时显示初始状态
        document.addEventListener('DOMContentLoaded', function() {
            updateTestStatus('备份功能测试工具已就绪，请开始测试')
        })
    </script>
</body>
</html>
