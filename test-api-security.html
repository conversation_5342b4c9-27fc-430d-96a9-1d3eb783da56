<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API安全测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        h2 {
            color: #666;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .test-section {
            margin: 20px 0;
        }
        input, button {
            padding: 10px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        input[type="text"], input[type="url"] {
            width: 300px;
        }
        button {
            background-color: #007cba;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #005a87;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔒 API安全测试工具</h1>
        <p>此工具用于测试API路由的安全性，验证认证机制是否正常工作。</p>
    </div>

    <div class="container">
        <h2>🚫 测试1: 无认证访问（应该被拒绝）</h2>
        <div class="test-section">
            <p>测试所有API路由在没有API密钥的情况下是否会被正确拒绝。</p>
            <button id="testNoAuth">测试无认证访问</button>
            <div id="noAuthResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <div class="container">
        <h2>🔑 测试2: 错误API密钥（应该被拒绝）</h2>
        <div class="test-section">
            <p>测试使用错误的API密钥是否会被正确拒绝。</p>
            <input type="text" id="wrongApiKey" placeholder="输入错误的API密钥" value="wrong-api-key-123">
            <button id="testWrongAuth">测试错误认证</button>
            <div id="wrongAuthResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <div class="container">
        <h2>✅ 测试3: 正确API密钥（应该成功）</h2>
        <div class="test-section">
            <p>测试使用正确的API密钥是否可以正常访问。</p>
            <input type="text" id="correctApiKey" placeholder="输入正确的API密钥">
            <input type="url" id="testUrl" placeholder="输入要测试的URL" value="https://www.baidu.com">
            <button id="testCorrectAuth">测试正确认证</button>
            <div id="correctAuthResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <div class="container">
        <h2>📊 测试结果总结</h2>
        <div id="summary" class="result info" style="display: none;"></div>
    </div>

    <script>
        const API_BASE = window.location.origin;
        const API_ENDPOINTS = [
            { name: 'analyze-url', method: 'POST', path: '/api/analyze-url', body: { url: 'https://www.baidu.com' } },
            { name: 'deep-search', method: 'POST', path: '/api/deep-search', body: { query: 'test', tools: [] } },
            { name: 'global-search', method: 'POST', path: '/api/global-search', body: { query: 'test' } },
            { name: 'fetch-content', method: 'GET', path: '/api/fetch-content?url=https://www.baidu.com', body: null }
        ];

        let testResults = [];

        // 测试无认证访问
        document.getElementById('testNoAuth').addEventListener('click', async () => {
            const button = document.getElementById('testNoAuth');
            const resultDiv = document.getElementById('noAuthResult');
            
            button.disabled = true;
            button.textContent = '测试中...';
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '正在测试无认证访问...';

            const results = [];
            
            for (const endpoint of API_ENDPOINTS) {
                try {
                    const options = {
                        method: endpoint.method,
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    };
                    
                    if (endpoint.body) {
                        options.body = JSON.stringify(endpoint.body);
                    }

                    const response = await fetch(`${API_BASE}${endpoint.path}`, options);
                    const data = await response.json();
                    
                    if (response.status === 401 || response.status === 403) {
                        results.push(`✅ ${endpoint.name}: 正确拒绝 (${response.status})`);
                    } else {
                        results.push(`❌ ${endpoint.name}: 意外允许访问 (${response.status})`);
                    }
                } catch (error) {
                    results.push(`⚠️ ${endpoint.name}: 网络错误 - ${error.message}`);
                }
            }

            const allRejected = results.every(r => r.includes('✅'));
            resultDiv.className = `result ${allRejected ? 'success' : 'error'}`;
            resultDiv.textContent = `无认证访问测试结果:\n\n${results.join('\n')}\n\n${allRejected ? '🎉 所有API都正确拒绝了无认证访问！' : '⚠️ 存在安全漏洞：某些API允许无认证访问！'}`;
            
            testResults.push({ test: '无认证访问', passed: allRejected, details: results });
            updateSummary();
            
            button.disabled = false;
            button.textContent = '测试无认证访问';
        });

        // 测试错误API密钥
        document.getElementById('testWrongAuth').addEventListener('click', async () => {
            const button = document.getElementById('testWrongAuth');
            const resultDiv = document.getElementById('wrongAuthResult');
            const wrongKey = document.getElementById('wrongApiKey').value;
            
            if (!wrongKey.trim()) {
                alert('请输入错误的API密钥');
                return;
            }

            button.disabled = true;
            button.textContent = '测试中...';
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '正在测试错误API密钥...';

            const results = [];
            
            for (const endpoint of API_ENDPOINTS) {
                try {
                    const options = {
                        method: endpoint.method,
                        headers: {
                            'Content-Type': 'application/json',
                            'X-API-Key': wrongKey
                        }
                    };
                    
                    if (endpoint.body) {
                        options.body = JSON.stringify(endpoint.body);
                    }

                    const response = await fetch(`${API_BASE}${endpoint.path}`, options);
                    const data = await response.json();
                    
                    if (response.status === 401 || response.status === 403) {
                        results.push(`✅ ${endpoint.name}: 正确拒绝错误密钥 (${response.status})`);
                    } else {
                        results.push(`❌ ${endpoint.name}: 意外接受错误密钥 (${response.status})`);
                    }
                } catch (error) {
                    results.push(`⚠️ ${endpoint.name}: 网络错误 - ${error.message}`);
                }
            }

            const allRejected = results.every(r => r.includes('✅'));
            resultDiv.className = `result ${allRejected ? 'success' : 'error'}`;
            resultDiv.textContent = `错误API密钥测试结果:\n\n${results.join('\n')}\n\n${allRejected ? '🎉 所有API都正确拒绝了错误的API密钥！' : '⚠️ 存在安全漏洞：某些API接受了错误的API密钥！'}`;
            
            testResults.push({ test: '错误API密钥', passed: allRejected, details: results });
            updateSummary();
            
            button.disabled = false;
            button.textContent = '测试错误认证';
        });

        // 测试正确API密钥
        document.getElementById('testCorrectAuth').addEventListener('click', async () => {
            const button = document.getElementById('testCorrectAuth');
            const resultDiv = document.getElementById('correctAuthResult');
            const correctKey = document.getElementById('correctApiKey').value;
            const testUrl = document.getElementById('testUrl').value;
            
            if (!correctKey.trim()) {
                alert('请输入正确的API密钥');
                return;
            }

            button.disabled = true;
            button.textContent = '测试中...';
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '正在测试正确API密钥...';

            const results = [];
            
            // 更新测试数据
            const testEndpoints = API_ENDPOINTS.map(ep => ({
                ...ep,
                body: ep.name === 'analyze-url' ? { url: testUrl } : ep.body,
                path: ep.name === 'fetch-content' ? `/api/fetch-content?url=${encodeURIComponent(testUrl)}` : ep.path
            }));
            
            for (const endpoint of testEndpoints) {
                try {
                    const options = {
                        method: endpoint.method,
                        headers: {
                            'Content-Type': 'application/json',
                            'X-API-Key': correctKey
                        }
                    };
                    
                    if (endpoint.body) {
                        options.body = JSON.stringify(endpoint.body);
                    }

                    const response = await fetch(`${API_BASE}${endpoint.path}`, options);
                    const data = await response.json();
                    
                    if (response.ok) {
                        results.push(`✅ ${endpoint.name}: 成功访问 (${response.status})`);
                    } else if (response.status === 401 || response.status === 403) {
                        results.push(`❌ ${endpoint.name}: 认证失败，可能API密钥错误 (${response.status})`);
                    } else {
                        results.push(`⚠️ ${endpoint.name}: 其他错误 (${response.status}) - ${data.error || '未知错误'}`);
                    }
                } catch (error) {
                    results.push(`⚠️ ${endpoint.name}: 网络错误 - ${error.message}`);
                }
            }

            const allSuccess = results.every(r => r.includes('✅'));
            resultDiv.className = `result ${allSuccess ? 'success' : 'warning'}`;
            resultDiv.textContent = `正确API密钥测试结果:\n\n${results.join('\n')}\n\n${allSuccess ? '🎉 所有API都可以正常访问！' : '⚠️ 某些API访问失败，请检查API密钥或服务状态。'}`;
            
            testResults.push({ test: '正确API密钥', passed: allSuccess, details: results });
            updateSummary();
            
            button.disabled = false;
            button.textContent = '测试正确认证';
        });

        function updateSummary() {
            const summaryDiv = document.getElementById('summary');
            if (testResults.length === 0) return;

            summaryDiv.style.display = 'block';
            
            const totalTests = testResults.length;
            const passedTests = testResults.filter(r => r.passed).length;
            const failedTests = totalTests - passedTests;

            let summaryText = `📊 测试总结:\n\n`;
            summaryText += `总测试数: ${totalTests}\n`;
            summaryText += `通过: ${passedTests}\n`;
            summaryText += `失败: ${failedTests}\n\n`;

            testResults.forEach((result, index) => {
                summaryText += `${index + 1}. ${result.test}: ${result.passed ? '✅ 通过' : '❌ 失败'}\n`;
            });

            if (passedTests === totalTests) {
                summaryText += `\n🎉 所有安全测试都通过了！API认证机制工作正常。`;
                summaryDiv.className = 'result success';
            } else {
                summaryText += `\n⚠️ 存在安全问题，请检查失败的测试项目。`;
                summaryDiv.className = 'result error';
            }

            summaryDiv.textContent = summaryText;
        }
    </script>
</body>
</html>
