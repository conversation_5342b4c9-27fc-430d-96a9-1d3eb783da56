# ToolMaster Vercel 部署指南

## 🚀 快速部署流程

### 1. 准备工作

#### 1.1 获取 DeepSeek API Key
1. 访问 [DeepSeek 平台](https://platform.deepseek.com/)
2. 注册/登录账号
3. 在 API Keys 页面创建新的 API Key
4. 复制保存 API Key（格式：`sk-xxxxxx`）

#### 1.2 创建 Supabase 项目
1. 访问 [Supabase](https://supabase.com/)
2. 注册/登录账号
3. 创建新项目
4. 在项目设置 → API 页面获取：
   - `Project URL`
   - `anon public` key

#### 1.3 设置访问密码
- 决定页面访问密码（支持多个，用逗号分隔）
- 例如：`password123,admin2024,secure123`

### 2. Vercel 部署

#### 方式一：通过 Vercel Dashboard（推荐）

1. **导入项目**
   - 访问 [Vercel Dashboard](https://vercel.com/dashboard)
   - 点击 "New Project"
   - 导入你的 GitHub 仓库

2. **配置环境变量**
   在部署配置页面添加以下环境变量：

   ```
   ACCESS_PASSWORDS=your-passwords-here
   API_ACCESS_KEY=your-random-api-access-key-here
   DEEPSEEK_API_KEY=your-deepseek-api-key
   DEEPSEEK_API_URL=https://api.deepseek.com/chat/completions
   NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
   ```

3. **部署**
   - 点击 "Deploy" 开始部署
   - 等待构建完成

#### 方式二：通过 Vercel CLI

1. **安装 Vercel CLI**
   ```bash
   npm i -g vercel
   ```

2. **登录 Vercel**
   ```bash
   vercel login
   ```

3. **部署项目**
   ```bash
   # 在项目根目录执行
   vercel
   
   # 按提示配置项目
   # 选择团队、项目名称等
   ```

4. **添加环境变量**
   ```bash
   vercel env add ACCESS_PASSWORDS
   vercel env add API_ACCESS_KEY
   vercel env add DEEPSEEK_API_KEY
   vercel env add DEEPSEEK_API_URL
   vercel env add NEXT_PUBLIC_SUPABASE_URL
   vercel env add NEXT_PUBLIC_SUPABASE_ANON_KEY
   ```

5. **重新部署**
   ```bash
   vercel --prod
   ```

### 3. 环境变量配置详情

| 变量名 | 必填 | 说明 | 示例值 |
|--------|------|------|--------|
| `ACCESS_PASSWORDS` | 是 | 页面访问密码，多个用逗号分隔 | `password123,admin2024` |
| `API_ACCESS_KEY` | 是 | API访问密钥，用于保护API路由 | `abc123xyz789random` |
| `DEEPSEEK_API_KEY` | 是 | DeepSeek API 密钥 | `sk-xxxxxxxxxxxxxx` |
| `DEEPSEEK_API_URL` | 否 | DeepSeek API 地址 | `https://api.deepseek.com/chat/completions` |
| `NEXT_PUBLIC_SUPABASE_URL` | 是 | Supabase 项目 URL | `https://xxx.supabase.co` |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | 是 | Supabase 匿名密钥 | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` |

### 4. 验证部署

1. **访问应用**
   - 打开 Vercel 提供的域名
   - 输入配置的访问密码
   - 确认可以正常进入应用

2. **测试功能**
   - 尝试添加工具
   - 测试 AI 分析功能
   - 验证数据同步功能

### 5. 常见问题

#### 5.1 部署失败
- 检查环境变量是否正确配置
- 确认 API Key 是否有效
- 查看 Vercel 构建日志

#### 5.2 AI 功能不工作
- 确认 `DEEPSEEK_API_KEY` 是否正确
- 检查 DeepSeek 账户余额
- 验证 API Key 权限

#### 5.3 数据不同步
- 确认 Supabase 配置是否正确
- 检查 Supabase 项目是否正常运行
- 验证数据库表是否创建

### 6. 更新部署

当代码更新后，Vercel 会自动重新部署：
- 推送代码到 GitHub
- Vercel 自动检测并重新部署
- 无需手动操作

### 7. 自定义域名（可选）

1. 在 Vercel Dashboard 中选择项目
2. 进入 "Domains" 设置
3. 添加自定义域名
4. 按提示配置 DNS 记录

## 🎉 部署完成

恭喜！你的 ToolMaster 应用已成功部署到 Vercel。现在可以通过提供的 URL 访问你的工具管理平台了。
